# نظام الرقيب - Registration & Tracking System

نظام متكامل لتسجيل ومتابعة شركات استيراد وتصدير الأدوية والمواد الغذائية في ليبيا

## المميزات الرئيسية

### 🏢 إدارة المنشآت والتراخيص
- تسجيل المنشآت الغذائية والدوائية
- إدارة التراخيص والموافقات
- إصدار الشهادات الرقمية
- نظام تنبيهات انتهاء الصلاحية

### 🚢 إدارة الشحنات والمنافذ
- تتبع الشحنات الواردة والصادرة
- إدارة عمليات التفتيش الميداني
- إصدار قرارات الإفراج/الرفض
- ربط مع جميع المنافذ (بحرية، جوية، برية)

### 🔬 نظام إدارة المختبرات (LIMS)
- إدارة العينات والتحاليل
- تتبع نتائج الفحوصات
- إصدار شهادات التحليل
- ربط النتائج بالمواصفات القياسية

### 📊 التقارير والإحصائيات
- لوحات معلومات تفاعلية
- تقارير مفصلة وقابلة للتخصيص
- إحصائيات في الوقت الفعلي
- تحليلات متقدمة

### 🔐 نظام أمان متقدم
- نظام أدوار وصلاحيات (RBAC)
- تشفير البيانات الحساسة
- سجل مراجعة شامل (Audit Trail)
- جلسات آمنة مع انتهاء صلاحية

## متطلبات النظام

### الخادم
- **PHP**: 8.0 أو أحدث
- **MySQL**: 8.0 أو أحدث
- **Apache/Nginx**: مع دعم mod_rewrite
- **الذاكرة**: 512 MB RAM كحد أدنى
- **التخزين**: 1 GB مساحة فارغة

### المتصفح
- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

## التثبيت والإعداد

### 1. تحضير البيئة

```bash
# تأكد من تشغيل XAMPP أو WAMP
# تأكد من تفعيل PHP extensions التالية:
# - pdo_mysql
# - mbstring
# - openssl
# - json
# - curl
```

### 2. تحميل الملفات

```bash
# انسخ ملفات المشروع إلى مجلد htdocs
cp -r EFDCLY/ /xampp/htdocs/
```

### 3. إعداد قاعدة البيانات

```sql
-- إنشاء قاعدة البيانات
CREATE DATABASE efdcly_raqeeb CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- استيراد الهيكل الأساسي
SOURCE database/schema.sql;

-- استيراد البيانات الأولية
SOURCE database/seed_data.sql;
```

### 4. تعديل إعدادات الاتصال

```php
// في ملف config/database.php
define('DB_HOST', 'localhost');
define('DB_NAME', 'efdcly_raqeeb');
define('DB_USER', 'root');
define('DB_PASS', '');
```

### 5. إنشاء المستخدم المدير

```bash
# تشغيل سكريبت إنشاء المدير
php database/create_admin.php
```

### 6. ضبط الصلاحيات

```bash
# تأكد من صلاحيات الكتابة لمجلد الرفع
chmod 755 public/uploads/
```

## بيانات الدخول الافتراضية

- **اسم المستخدم**: admin
- **كلمة المرور**: admin123
- **البريد الإلكتروني**: <EMAIL>

> ⚠️ **تحذير**: يرجى تغيير كلمة المرور فور تسجيل الدخول الأول

## هيكل المشروع

```
EFDCLY/
├── config/                 # ملفات الإعدادات
│   ├── config.php         # الإعدادات العامة
│   └── database.php       # إعدادات قاعدة البيانات
├── database/              # قاعدة البيانات
│   ├── schema.sql         # هيكل قاعدة البيانات
│   ├── seed_data.sql      # البيانات الأولية
│   └── create_admin.php   # إنشاء المدير
├── includes/              # الملفات المشتركة
│   └── auth_check.php     # فحص المصادقة
├── modules/               # وحدات النظام
│   ├── auth/              # نظام المصادقة
│   ├── dashboard/         # لوحة التحكم
│   ├── facilities/        # إدارة المنشآت
│   ├── shipments/         # إدارة الشحنات
│   ├── laboratory/        # إدارة المختبرات
│   ├── inspection/        # إدارة التفتيش
│   ├── complaints/        # إدارة الشكاوى
│   └── reports/           # التقارير
├── templates/             # قوالب الواجهات
├── assets/                # الملفات الثابتة
│   ├── css/              # ملفات الأنماط
│   ├── js/               # ملفات JavaScript
│   └── images/           # الصور
├── public/                # الملفات العامة
│   └── uploads/          # ملفات الرفع
└── index.php             # الصفحة الرئيسية
```

## الوحدات الرئيسية

### 1. وحدة المصادقة (Authentication)
- تسجيل الدخول والخروج
- إدارة الجلسات
- نظام الأدوار والصلاحيات

### 2. وحدة إدارة المنشآت
- تسجيل المنشآت الجديدة
- إدارة التراخيص
- إصدار الشهادات

### 3. وحدة إدارة الشحنات
- تسجيل الشحنات
- عمليات التفتيش
- قرارات الإفراج

### 4. وحدة المختبرات
- إدارة العينات
- تسجيل النتائج
- إصدار الشهادات

## الدعم والمساعدة

### التوثيق
- [دليل المستخدم](docs/user-guide.md)
- [دليل المطور](docs/developer-guide.md)
- [API Documentation](docs/api.md)

### المساهمة
نرحب بمساهماتكم في تطوير النظام. يرجى قراءة [دليل المساهمة](CONTRIBUTING.md) قبل البدء.

### الترخيص
هذا المشروع مرخص تحت [رخصة MIT](LICENSE).

---

© 2025 نظام الرقيب - جميع الحقوق محفوظة