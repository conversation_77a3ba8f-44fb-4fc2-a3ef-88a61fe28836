<?php
/**
 * نظام الرقيب - تبديل نوع قاعدة البيانات
 * Database Type Switcher
 * Created: 2025-08-12
 */

// التحقق من تشغيل السكريبت من سطر الأوامر أو المتصفح
$isCommandLine = php_sapi_name() === 'cli';

if (!$isCommandLine) {
    echo "<!DOCTYPE html><html dir='rtl'><head><meta charset='UTF-8'><title>تبديل نوع قاعدة البيانات</title>";
    echo "<style>body{font-family:Arial;margin:40px;background:#f5f5f5;} .container{background:white;padding:30px;border-radius:10px;box-shadow:0 2px 10px rgba(0,0,0,0.1);} .success{color:#28a745;} .error{color:#dc3545;} .info{color:#17a2b8;} .warning{color:#ffc107;} .btn{background:#007bff;color:white;padding:10px 20px;border:none;border-radius:5px;text-decoration:none;display:inline-block;margin:5px;} .btn:hover{background:#0056b3;}</style></head><body>";
    echo "<div class='container'><h1>🛡️ نظام الرقيب - تبديل نوع قاعدة البيانات</h1>";
}

/**
 * طباعة رسالة مع تنسيق مناسب للمتصفح أو سطر الأوامر
 */
function printMessage($message, $type = 'info') {
    global $isCommandLine;

    if ($isCommandLine) {
        $prefix = '';
        switch ($type) {
            case 'success': $prefix = '✅ '; break;
            case 'error': $prefix = '❌ '; break;
            case 'warning': $prefix = '⚠️ '; break;
            case 'info': $prefix = 'ℹ️ '; break;
        }
        echo $prefix . $message . "\n";
    } else {
        $class = $type;
        echo "<p class='$class'>$message</p>";
    }
}

// قراءة النوع الحالي
$configFile = '../config/database.php';
$configContent = file_get_contents($configFile);

// استخراج النوع الحالي
preg_match("/define\('DB_TYPE',\s*'(\w+)'\);/", $configContent, $matches);
$currentType = isset($matches[1]) ? $matches[1] : 'mysql';

// معالجة طلب التبديل
if (isset($_GET['switch_to'])) {
    $newType = $_GET['switch_to'];

    if ($newType === 'mysql' || $newType === 'sqlserver') {
        // تحديث ملف الإعدادات
        $newConfigContent = preg_replace(
            "/define\('DB_TYPE',\s*'(\w+)'\);/",
            "define('DB_TYPE', '$newType');",
            $configContent
        );

        if (file_put_contents($configFile, $newConfigContent)) {
            printMessage("تم تبديل نوع قاعدة البيانات إلى: $newType", 'success');
            $currentType = $newType;
        } else {
            printMessage("فشل في تحديث ملف الإعدادات", 'error');
        }
    } else {
        printMessage("نوع قاعدة البيانات غير صحيح", 'error');
    }
}

// عرض الحالة الحالية
printMessage("نوع قاعدة البيانات الحالي: " . strtoupper($currentType), 'info');

if (!$isCommandLine) {
    echo "<h3>تبديل نوع قاعدة البيانات:</h3>";

    if ($currentType === 'mysql') {
        echo "<p>أنت تستخدم حالياً <strong>MySQL</strong></p>";
        echo "<a href='?switch_to=sqlserver' class='btn'>التبديل إلى SQL Server</a>";
        echo "<a href='setup_database.php' class='btn'>إعداد MySQL</a>";
        echo "<a href='check_system.php' class='btn'>فحص MySQL</a>";
    } else {
        echo "<p>أنت تستخدم حالياً <strong>SQL Server</strong></p>";
        echo "<a href='?switch_to=mysql' class='btn'>التبديل إلى MySQL</a>";
        echo "<a href='setup_sqlserver.php' class='btn'>إعداد SQL Server</a>";
        echo "<a href='check_sqlserver.php' class='btn'>فحص SQL Server</a>";
    }

    echo "<h3>معلومات مهمة:</h3>";
    echo "<ul>";
    echo "<li><strong>MySQL</strong>: يتطلب XAMPP مع MySQL مفعل</li>";
    echo "<li><strong>SQL Server</strong>: يتطلب Microsoft SQL Server و Microsoft Drivers for PHP</li>";
    echo "<li>بعد التبديل، تحتاج لإعداد قاعدة البيانات الجديدة</li>";
    echo "<li>البيانات لن تنتقل تلقائياً بين النوعين</li>";
    echo "</ul>";

    echo "<h3>الخطوات التالية:</h3>";
    echo "<ol>";
    echo "<li>اختر نوع قاعدة البيانات المطلوب</li>";
    echo "<li>اضغط على زر الإعداد المناسب</li>";
    echo "<li>اتبع التعليمات لإنشاء قاعدة البيانات</li>";
    echo "<li>استخدم أداة الفحص للتأكد من سلامة النظام</li>";
    echo "</ol>";

    // فحص الإضافات المطلوبة
    echo "<h3>فحص الإضافات المطلوبة:</h3>";

    // فحص MySQL
    if (extension_loaded('pdo_mysql')) {
        echo "<div class='success'>✅ إضافة MySQL PDO متوفرة</div>";
    } else {
        echo "<div class='error'>❌ إضافة MySQL PDO غير متوفرة</div>";
    }

    // فحص SQL Server
    if (extension_loaded('pdo_sqlsrv')) {
        echo "<div class='success'>✅ إضافة SQL Server PDO متوفرة</div>";
    } else {
        echo "<div class='warning'>⚠️ إضافة SQL Server PDO غير متوفرة</div>";
        echo "<p><small>لاستخدام SQL Server، يرجى تثبيت Microsoft Drivers for PHP for SQL Server</small></p>";
        echo "<p><a href='install_sqlserver_drivers.php' class='btn'>📥 دليل تثبيت SQL Server Drivers</a></p>";
    }

    echo "</div></body></html>";
} else {
    // في سطر الأوامر
    echo "\nالخيارات المتاحة:\n";
    echo "php switch_database.php mysql     - التبديل إلى MySQL\n";
    echo "php switch_database.php sqlserver - التبديل إلى SQL Server\n";

    if (isset($argv[1])) {
        $newType = $argv[1];
        if ($newType === 'mysql' || $newType === 'sqlserver') {
            $newConfigContent = preg_replace(
                "/define\('DB_TYPE',\s*'(\w+)'\);/",
                "define('DB_TYPE', '$newType');",
                $configContent
            );

            if (file_put_contents($configFile, $newConfigContent)) {
                printMessage("تم تبديل نوع قاعدة البيانات إلى: $newType", 'success');
            } else {
                printMessage("فشل في تحديث ملف الإعدادات", 'error');
            }
        } else {
            printMessage("نوع قاعدة البيانات غير صحيح. استخدم: mysql أو sqlserver", 'error');
        }
    }
}
?>