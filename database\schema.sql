-- نظام الرقيب - قاعدة البيانات الشاملة
-- Registration & Tracking System for Food & Drug Import/Export Companies in Libya
-- Created: 2025-08-12

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ===================================
-- جداول النظام الأساسية
-- ===================================

-- جدول المستخدمين
CREATE TABLE users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    full_name VARCHAR(100) NOT NULL,
    phone VARCHAR(20),
    role_id INT NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    last_login TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by INT,
    INDEX idx_username (username),
    INDEX idx_email (email),
    INDEX idx_role (role_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول الأدوار والصلاحيات
CREATE TABLE roles (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(50) NOT NULL,
    name_ar VARCHAR(100) NOT NULL,
    description TEXT,
    permissions JSON,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول الصلاحيات
CREATE TABLE permissions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(50) NOT NULL,
    name_ar VARCHAR(100) NOT NULL,
    module VARCHAR(50) NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول ربط الأدوار بالصلاحيات
CREATE TABLE role_permissions (
    role_id INT NOT NULL,
    permission_id INT NOT NULL,
    PRIMARY KEY (role_id, permission_id),
    FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE,
    FOREIGN KEY (permission_id) REFERENCES permissions(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ===================================
-- الوحدة الأولى: إدارة المنشآت والتراخيص
-- ===================================

-- جدول أنواع المنشآت
CREATE TABLE facility_types (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    name_ar VARCHAR(150) NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول المنشآت
CREATE TABLE facilities (
    id INT PRIMARY KEY AUTO_INCREMENT,
    registration_number VARCHAR(50) UNIQUE NOT NULL,
    commercial_register VARCHAR(50) NOT NULL,
    name VARCHAR(200) NOT NULL,
    name_ar VARCHAR(300) NOT NULL,
    facility_type_id INT NOT NULL,
    license_number VARCHAR(50),
    license_issue_date DATE,
    license_expiry_date DATE,
    license_status ENUM('active', 'expired', 'suspended', 'cancelled') DEFAULT 'active',
    address TEXT NOT NULL,
    city VARCHAR(100) NOT NULL,
    phone VARCHAR(20),
    email VARCHAR(100),
    contact_person VARCHAR(100),
    contact_phone VARCHAR(20),
    activities JSON, -- أنشطة المنشأة
    products JSON, -- المنتجات المسجلة
    certifications JSON, -- الشهادات (HACCP, GMP, etc.)
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by INT,
    FOREIGN KEY (facility_type_id) REFERENCES facility_types(id),
    FOREIGN KEY (created_by) REFERENCES users(id),
    INDEX idx_registration_number (registration_number),
    INDEX idx_commercial_register (commercial_register),
    INDEX idx_license_number (license_number),
    INDEX idx_license_status (license_status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول طلبات التسجيل والترخيص
CREATE TABLE registration_requests (
    id INT PRIMARY KEY AUTO_INCREMENT,
    request_number VARCHAR(50) UNIQUE NOT NULL,
    facility_id INT,
    request_type ENUM('new_registration', 'license_renewal', 'data_update') NOT NULL,
    status ENUM('pending', 'under_review', 'approved', 'rejected', 'requires_documents') DEFAULT 'pending',
    submitted_data JSON,
    documents JSON, -- المستندات المرفقة
    review_notes TEXT,
    reviewed_by INT,
    reviewed_at TIMESTAMP NULL,
    approved_by INT,
    approved_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (facility_id) REFERENCES facilities(id),
    FOREIGN KEY (reviewed_by) REFERENCES users(id),
    FOREIGN KEY (approved_by) REFERENCES users(id),
    INDEX idx_request_number (request_number),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول الشهادات الرقمية
CREATE TABLE digital_certificates (
    id INT PRIMARY KEY AUTO_INCREMENT,
    certificate_number VARCHAR(50) UNIQUE NOT NULL,
    facility_id INT NOT NULL,
    certificate_type ENUM('registration', 'license', 'compliance') NOT NULL,
    issue_date DATE NOT NULL,
    expiry_date DATE,
    qr_code TEXT,
    verification_code VARCHAR(100) UNIQUE,
    is_valid BOOLEAN DEFAULT TRUE,
    issued_by INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (facility_id) REFERENCES facilities(id),
    FOREIGN KEY (issued_by) REFERENCES users(id),
    INDEX idx_certificate_number (certificate_number),
    INDEX idx_verification_code (verification_code)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ===================================
-- الوحدة الثانية: إدارة المنافذ والشحنات
-- ===================================

-- جدول المنافذ
CREATE TABLE ports (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    name_ar VARCHAR(150) NOT NULL,
    type ENUM('sea', 'air', 'land') NOT NULL,
    code VARCHAR(10) UNIQUE NOT NULL,
    city VARCHAR(100) NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول الشحنات
CREATE TABLE shipments (
    id INT PRIMARY KEY AUTO_INCREMENT,
    shipment_number VARCHAR(50) UNIQUE NOT NULL,
    facility_id INT NOT NULL,
    port_id INT NOT NULL,
    shipment_type ENUM('import', 'export') NOT NULL,
    arrival_date DATE,
    notification_date TIMESTAMP,
    bill_of_lading VARCHAR(100),
    invoice_number VARCHAR(100),
    origin_country VARCHAR(100),
    destination_country VARCHAR(100),
    product_description TEXT,
    quantity DECIMAL(10,2),
    unit VARCHAR(20),
    value DECIMAL(12,2),
    currency VARCHAR(3),
    status ENUM('notified', 'under_inspection', 'released', 'rejected', 'held') DEFAULT 'notified',
    documents JSON, -- المستندات المرفقة
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (facility_id) REFERENCES facilities(id),
    FOREIGN KEY (port_id) REFERENCES ports(id),
    INDEX idx_shipment_number (shipment_number),
    INDEX idx_status (status),
    INDEX idx_arrival_date (arrival_date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول التفتيش الميداني
CREATE TABLE inspections (
    id INT PRIMARY KEY AUTO_INCREMENT,
    inspection_number VARCHAR(50) UNIQUE NOT NULL,
    shipment_id INT NOT NULL,
    inspector_id INT NOT NULL,
    inspection_date DATE NOT NULL,
    inspection_type ENUM('document_review', 'physical_inspection', 'sampling') NOT NULL,
    checklist_data JSON, -- بيانات قائمة التحقق
    findings TEXT,
    photos JSON, -- الصور المرفقة
    status ENUM('pending', 'completed', 'requires_follow_up') DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (shipment_id) REFERENCES shipments(id),
    FOREIGN KEY (inspector_id) REFERENCES users(id),
    INDEX idx_inspection_number (inspection_number),
    INDEX idx_inspection_date (inspection_date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول قرارات الإفراج/الرفض
CREATE TABLE release_decisions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    decision_number VARCHAR(50) UNIQUE NOT NULL,
    shipment_id INT NOT NULL,
    decision_type ENUM('release', 'conditional_release', 'reject', 'hold') NOT NULL,
    decision_date DATE NOT NULL,
    decision_reason TEXT,
    conditions TEXT, -- شروط الإفراج المؤقت
    decided_by INT NOT NULL,
    valid_until DATE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (shipment_id) REFERENCES shipments(id),
    FOREIGN KEY (decided_by) REFERENCES users(id),
    INDEX idx_decision_number (decision_number),
    INDEX idx_decision_date (decision_date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ===================================
-- الوحدة الثالثة: إدارة المختبرات (LIMS)
-- ===================================

-- جدول العينات
CREATE TABLE samples (
    id INT PRIMARY KEY AUTO_INCREMENT,
    sample_number VARCHAR(50) UNIQUE NOT NULL,
    barcode VARCHAR(100) UNIQUE,
    shipment_id INT,
    facility_id INT,
    sample_type ENUM('shipment', 'market', 'complaint') NOT NULL,
    product_name VARCHAR(200) NOT NULL,
    batch_number VARCHAR(100),
    production_date DATE,
    expiry_date DATE,
    sampling_date DATE NOT NULL,
    sampling_location VARCHAR(200),
    sampled_by INT NOT NULL,
    sample_condition TEXT,
    storage_conditions VARCHAR(100),
    status ENUM('received', 'under_analysis', 'completed', 'rejected') DEFAULT 'received',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (shipment_id) REFERENCES shipments(id),
    FOREIGN KEY (facility_id) REFERENCES facilities(id),
    FOREIGN KEY (sampled_by) REFERENCES users(id),
    INDEX idx_sample_number (sample_number),
    INDEX idx_barcode (barcode),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول أنواع التحاليل
CREATE TABLE test_types (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    name_ar VARCHAR(150) NOT NULL,
    category ENUM('microbiological', 'chemical', 'physical', 'nutritional') NOT NULL,
    method VARCHAR(200),
    unit VARCHAR(50),
    normal_range VARCHAR(100),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول نتائج التحاليل
CREATE TABLE test_results (
    id INT PRIMARY KEY AUTO_INCREMENT,
    sample_id INT NOT NULL,
    test_type_id INT NOT NULL,
    result_value VARCHAR(200),
    result_status ENUM('pass', 'fail', 'pending', 'inconclusive') DEFAULT 'pending',
    test_date DATE NOT NULL,
    tested_by INT NOT NULL,
    reviewed_by INT,
    reviewed_at TIMESTAMP NULL,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (sample_id) REFERENCES samples(id),
    FOREIGN KEY (test_type_id) REFERENCES test_types(id),
    FOREIGN KEY (tested_by) REFERENCES users(id),
    FOREIGN KEY (reviewed_by) REFERENCES users(id),
    INDEX idx_test_date (test_date),
    INDEX idx_result_status (result_status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول شهادات التحليل
CREATE TABLE analysis_certificates (
    id INT PRIMARY KEY AUTO_INCREMENT,
    certificate_number VARCHAR(50) UNIQUE NOT NULL,
    sample_id INT NOT NULL,
    issue_date DATE NOT NULL,
    overall_result ENUM('pass', 'fail') NOT NULL,
    recommendations TEXT,
    issued_by INT NOT NULL,
    approved_by INT,
    approved_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (sample_id) REFERENCES samples(id),
    FOREIGN KEY (issued_by) REFERENCES users(id),
    FOREIGN KEY (approved_by) REFERENCES users(id),
    INDEX idx_certificate_number (certificate_number),
    INDEX idx_issue_date (issue_date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ===================================
-- الوحدة الرابعة: التتبع وسلسلة الإمداد
-- ===================================

-- جدول تتبع الشحنات
CREATE TABLE shipment_tracking (
    id INT PRIMARY KEY AUTO_INCREMENT,
    shipment_id INT NOT NULL,
    status VARCHAR(50) NOT NULL,
    status_ar VARCHAR(100) NOT NULL,
    location VARCHAR(200),
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    notes TEXT,
    updated_by INT NOT NULL,
    FOREIGN KEY (shipment_id) REFERENCES shipments(id),
    FOREIGN KEY (updated_by) REFERENCES users(id),
    INDEX idx_shipment_id (shipment_id),
    INDEX idx_timestamp (timestamp)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ===================================
-- الوحدة الخامسة: الرقابة والتفتيش اللاحق
-- ===================================

-- جدول تفتيش المنشآت
CREATE TABLE facility_inspections (
    id INT PRIMARY KEY AUTO_INCREMENT,
    inspection_number VARCHAR(50) UNIQUE NOT NULL,
    facility_id INT NOT NULL,
    inspector_id INT NOT NULL,
    inspection_date DATE NOT NULL,
    inspection_type ENUM('routine', 'follow_up', 'complaint_based', 'random') NOT NULL,
    checklist_data JSON,
    violations JSON, -- المخالفات المرصودة
    photos JSON,
    overall_score DECIMAL(5,2),
    status ENUM('pending', 'completed', 'requires_follow_up') DEFAULT 'pending',
    next_inspection_date DATE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (facility_id) REFERENCES facilities(id),
    FOREIGN KEY (inspector_id) REFERENCES users(id),
    INDEX idx_inspection_number (inspection_number),
    INDEX idx_inspection_date (inspection_date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول المخالفات
CREATE TABLE violations (
    id INT PRIMARY KEY AUTO_INCREMENT,
    violation_code VARCHAR(20) NOT NULL,
    name VARCHAR(200) NOT NULL,
    name_ar VARCHAR(300) NOT NULL,
    category VARCHAR(100) NOT NULL,
    severity ENUM('minor', 'major', 'critical') NOT NULL,
    penalty_amount DECIMAL(10,2),
    description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول الإجراءات التصحيحية
CREATE TABLE corrective_actions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    facility_inspection_id INT NOT NULL,
    violation_id INT NOT NULL,
    required_action TEXT NOT NULL,
    deadline DATE NOT NULL,
    status ENUM('pending', 'in_progress', 'completed', 'overdue') DEFAULT 'pending',
    completion_date DATE,
    evidence JSON, -- أدلة التصحيح
    verified_by INT,
    verified_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (facility_inspection_id) REFERENCES facility_inspections(id),
    FOREIGN KEY (violation_id) REFERENCES violations(id),
    FOREIGN KEY (verified_by) REFERENCES users(id),
    INDEX idx_deadline (deadline),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ===================================
-- الوحدة السادسة: إدارة المخاطر
-- ===================================

-- جدول تقييم المخاطر
CREATE TABLE risk_assessments (
    id INT PRIMARY KEY AUTO_INCREMENT,
    assessment_number VARCHAR(50) UNIQUE NOT NULL,
    facility_id INT,
    product_category VARCHAR(100),
    risk_factors JSON,
    probability_score INT NOT NULL, -- 1-5
    impact_score INT NOT NULL, -- 1-5
    overall_risk_score DECIMAL(3,1), -- حاصل ضرب الاحتمالية والتأثير
    risk_level ENUM('low', 'medium', 'high', 'critical') NOT NULL,
    mitigation_measures TEXT,
    assessed_by INT NOT NULL,
    assessment_date DATE NOT NULL,
    review_date DATE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (facility_id) REFERENCES facilities(id),
    FOREIGN KEY (assessed_by) REFERENCES users(id),
    INDEX idx_assessment_number (assessment_number),
    INDEX idx_risk_level (risk_level)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ===================================
-- الوحدة السابعة: البلاغات والشكاوى
-- ===================================

-- جدول البلاغات والشكاوى
CREATE TABLE complaints (
    id INT PRIMARY KEY AUTO_INCREMENT,
    complaint_number VARCHAR(50) UNIQUE NOT NULL,
    complaint_type ENUM('product_quality', 'facility_violation', 'fraud', 'other') NOT NULL,
    source ENUM('public', 'internal', 'anonymous') NOT NULL,
    complainant_name VARCHAR(100),
    complainant_phone VARCHAR(20),
    complainant_email VARCHAR(100),
    facility_id INT,
    product_name VARCHAR(200),
    description TEXT NOT NULL,
    evidence JSON, -- أدلة مرفقة
    priority ENUM('low', 'medium', 'high', 'urgent') DEFAULT 'medium',
    status ENUM('received', 'under_investigation', 'resolved', 'closed') DEFAULT 'received',
    assigned_to INT,
    resolution TEXT,
    resolved_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (facility_id) REFERENCES facilities(id),
    FOREIGN KEY (assigned_to) REFERENCES users(id),
    INDEX idx_complaint_number (complaint_number),
    INDEX idx_status (status),
    INDEX idx_priority (priority)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ===================================
-- جداول النظام العامة
-- ===================================

-- جدول الإشعارات
CREATE TABLE notifications (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    title VARCHAR(200) NOT NULL,
    message TEXT NOT NULL,
    type ENUM('info', 'warning', 'success', 'error') DEFAULT 'info',
    is_read BOOLEAN DEFAULT FALSE,
    related_module VARCHAR(50),
    related_id INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id),
    INDEX idx_user_id (user_id),
    INDEX idx_is_read (is_read)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول سجل العمليات (Audit Trail)
CREATE TABLE audit_log (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT,
    action VARCHAR(100) NOT NULL,
    table_name VARCHAR(50) NOT NULL,
    record_id INT,
    old_values JSON,
    new_values JSON,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id),
    INDEX idx_user_id (user_id),
    INDEX idx_table_name (table_name),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول الإعدادات العامة
CREATE TABLE system_settings (
    id INT PRIMARY KEY AUTO_INCREMENT,
    setting_key VARCHAR(100) UNIQUE NOT NULL,
    setting_value TEXT,
    setting_type ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string',
    description TEXT,
    is_public BOOLEAN DEFAULT FALSE,
    updated_by INT,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (updated_by) REFERENCES users(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إضافة المفاتيح الخارجية المتبقية
ALTER TABLE users ADD FOREIGN KEY (role_id) REFERENCES roles(id);
ALTER TABLE users ADD FOREIGN KEY (created_by) REFERENCES users(id);

SET FOREIGN_KEY_CHECKS = 1;