<?php
/**
 * نظام الرقيب - فحص حالة النظام مع SQL Server
 * SQL Server System Health Check
 * Created: 2025-08-12
 */

// التحقق من تشغيل السكريبت من سطر الأوامر أو المتصفح
$isCommandLine = php_sapi_name() === 'cli';

if (!$isCommandLine) {
    echo "<!DOCTYPE html><html dir='rtl'><head><meta charset='UTF-8'><title>فحص حالة النظام - SQL Server</title>";
    echo "<style>body{font-family:Arial;margin:40px;background:#f5f5f5;} .container{background:white;padding:30px;border-radius:10px;box-shadow:0 2px 10px rgba(0,0,0,0.1);} .success{color:#28a745;} .error{color:#dc3545;} .info{color:#17a2b8;} .warning{color:#ffc107;} .check{background:#f8f9fa;padding:10px;margin:5px 0;border-radius:5px;}</style></head><body>";
    echo "<div class='container'><h1>🛡️ نظام الرقيب - فحص حالة النظام مع SQL Server</h1>";
}

/**
 * طباعة رسالة مع تنسيق مناسب للمتصفح أو سطر الأوامر
 */
function printMessage($message, $type = 'info') {
    global $isCommandLine;

    if ($isCommandLine) {
        $prefix = '';
        switch ($type) {
            case 'success': $prefix = '✅ '; break;
            case 'error': $prefix = '❌ '; break;
            case 'warning': $prefix = '⚠️ '; break;
            case 'info': $prefix = 'ℹ️ '; break;
        }
        echo $prefix . $message . "\n";
    } else {
        $class = $type;
        echo "<div class='check $class'>$message</div>";
    }
}

/**
 * طباعة عنوان
 */
function printHeader($header) {
    global $isCommandLine;

    if ($isCommandLine) {
        echo "\n" . str_repeat("=", 50) . "\n";
        echo $header . "\n";
        echo str_repeat("=", 50) . "\n";
    } else {
        echo "<h3>$header</h3>";
    }
}

$allChecks = true;

try {
    printHeader("🔍 فحص متطلبات PHP و SQL Server");

    // فحص إصدار PHP
    $phpVersion = phpversion();
    if (version_compare($phpVersion, '8.0.0', '>=')) {
        printMessage("إصدار PHP: $phpVersion ✓", 'success');
    } else {
        printMessage("إصدار PHP: $phpVersion - يتطلب 8.0 أو أحدث", 'error');
        $allChecks = false;
    }

    // فحص الإضافات المطلوبة لـ SQL Server
    $requiredExtensions = ['pdo', 'pdo_sqlsrv', 'mbstring', 'json', 'openssl'];
    foreach ($requiredExtensions as $ext) {
        if (extension_loaded($ext)) {
            printMessage("إضافة $ext: متوفرة ✓", 'success');
        } else {
            printMessage("إضافة $ext: غير متوفرة", 'error');
            if ($ext === 'pdo_sqlsrv') {
                printMessage("يرجى تثبيت Microsoft Drivers for PHP for SQL Server", 'error');
            }
            $allChecks = false;
        }
    }

    printHeader("📁 فحص الملفات والمجلدات");

    // فحص الملفات الأساسية
    $requiredFiles = [
        '../config/config.php' => 'ملف الإعدادات العامة',
        '../config/database.php' => 'ملف إعدادات قاعدة البيانات',
        '../modules/auth/User.php' => 'نموذج المستخدم',
        '../modules/auth/login.php' => 'صفحة تسجيل الدخول',
        '../modules/dashboard/index.php' => 'لوحة التحكم',
        '../includes/auth_check.php' => 'فحص المصادقة',
        'schema_sqlserver.sql' => 'هيكل قاعدة البيانات SQL Server',
        'seed_data_sqlserver.sql' => 'البيانات الأولية SQL Server'
    ];

    foreach ($requiredFiles as $file => $description) {
        if (file_exists($file)) {
            printMessage("$description: موجود ✓", 'success');
        } else {
            printMessage("$description: غير موجود ($file)", 'error');
            $allChecks = false;
        }
    }

    printHeader("🗄️ فحص قاعدة البيانات SQL Server");

    // محاولة الاتصال بقاعدة البيانات
    try {
        require_once '../config/config.php';
        $db = Database::getInstance()->getConnection();
        printMessage("الاتصال بقاعدة البيانات SQL Server: نجح ✓", 'success');

        // فحص إصدار SQL Server
        $stmt = $db->query("SELECT @@VERSION as version");
        $version = $stmt->fetch()['version'];
        printMessage("إصدار SQL Server: " . substr($version, 0, 100) . "...", 'info');

        // فحص الجداول الأساسية
        $requiredTables = [
            'users' => 'جدول المستخدمين',
            'roles' => 'جدول الأدوار',
            'permissions' => 'جدول الصلاحيات',
            'facilities' => 'جدول المنشآت',
            'shipments' => 'جدول الشحنات',
            'samples' => 'جدول العينات'
        ];

        foreach ($requiredTables as $table => $description) {
            $stmt = $db->query("SELECT COUNT(*) as count FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = '$table'");
            if ($stmt->fetch()['count'] > 0) {
                printMessage("$description: موجود ✓", 'success');
            } else {
                printMessage("$description: غير موجود", 'error');
                $allChecks = false;
            }
        }

        // فحص البيانات الأولية
        $stmt = $db->query("SELECT COUNT(*) as count FROM roles");
        $roleCount = $stmt->fetch()['count'];

        $stmt = $db->query("SELECT COUNT(*) as count FROM permissions");
        $permissionCount = $stmt->fetch()['count'];

        if ($roleCount > 0) {
            printMessage("البيانات الأولية للأدوار: $roleCount أدوار ✓", 'success');
        } else {
            printMessage("البيانات الأولية للأدوار: غير موجودة", 'error');
            $allChecks = false;
        }

        if ($permissionCount > 0) {
            printMessage("البيانات الأولية للصلاحيات: $permissionCount صلاحية ✓", 'success');
        } else {
            printMessage("البيانات الأولية للصلاحيات: غير موجودة", 'error');
            $allChecks = false;
        }

        // فحص المستخدم المدير
        require_once '../modules/auth/User.php';
        $userModel = new User();
        $adminUser = $userModel->findByUsername('admin');

        if ($adminUser) {
            printMessage("المستخدم المدير: موجود ✓", 'success');
            printMessage("اسم المستخدم: admin", 'info');
            printMessage("حالة النشاط: " . ($adminUser['is_active'] ? 'نشط' : 'غير نشط'), 'info');
        } else {
            printMessage("المستخدم المدير: غير موجود", 'error');
            $allChecks = false;
        }

        // فحص الترميز العربي
        $stmt = $db->query("SELECT N'اختبار الترميز العربي' as test_text");
        $testResult = $stmt->fetch()['test_text'];
        if ($testResult === 'اختبار الترميز العربي') {
            printMessage("الترميز العربي: يعمل بشكل صحيح ✓", 'success');
        } else {
            printMessage("الترميز العربي: مشكلة في الترميز", 'warning');
        }

    } catch (Exception $e) {
        printMessage("خطأ في قاعدة البيانات: " . $e->getMessage(), 'error');
        $allChecks = false;
    }

    printHeader("📋 النتيجة النهائية");

    if ($allChecks) {
        printMessage("🎉 جميع الفحوصات نجحت! النظام جاهز للاستخدام مع SQL Server", 'success');
        printMessage("يمكنك الآن الوصول للنظام عبر: http://localhost/EFDCLY", 'success');
        printMessage("بيانات الدخول: admin / admin123", 'info');
    } else {
        printMessage("❌ بعض الفحوصات فشلت. يرجى إصلاح المشاكل المذكورة أعلاه", 'error');
        printMessage("راجع ملف INSTALL.md للحصول على تعليمات التثبيت", 'info');
    }

} catch (Exception $e) {
    printMessage("خطأ عام في فحص النظام: " . $e->getMessage(), 'error');
}

if (!$isCommandLine) {
    echo "</div></body></html>";
}
?>