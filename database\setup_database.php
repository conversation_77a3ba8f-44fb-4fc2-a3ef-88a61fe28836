<?php
/**
 * نظام الرقيب - إعداد قاعدة البيانات الكامل
 * Complete Database Setup Script
 * Created: 2025-08-12
 */

// التحقق من تشغيل السكريبت من سطر الأوامر أو المتصفح
$isCommandLine = php_sapi_name() === 'cli';

if (!$isCommandLine) {
    echo "<!DOCTYPE html><html dir='rtl'><head><meta charset='UTF-8'><title>إعداد قاعدة البيانات</title>";
    echo "<style>body{font-family:Arial;margin:40px;background:#f5f5f5;} .container{background:white;padding:30px;border-radius:10px;box-shadow:0 2px 10px rgba(0,0,0,0.1);} .success{color:#28a745;} .error{color:#dc3545;} .info{color:#17a2b8;} .warning{color:#ffc107;} .step{background:#f8f9fa;padding:15px;margin:10px 0;border-left:4px solid #007bff;}</style></head><body>";
    echo "<div class='container'><h1>🛡️ نظام الرقيب - إعداد قاعدة البيانات</h1>";
}

/**
 * طباعة رسالة مع تنسيق مناسب للمتصفح أو سطر الأوامر
 */
function printMessage($message, $type = 'info') {
    global $isCommandLine;

    if ($isCommandLine) {
        $prefix = '';
        switch ($type) {
            case 'success': $prefix = '✅ '; break;
            case 'error': $prefix = '❌ '; break;
            case 'warning': $prefix = '⚠️ '; break;
            case 'info': $prefix = 'ℹ️ '; break;
        }
        echo $prefix . $message . "\n";
    } else {
        $class = $type;
        echo "<p class='$class'>$message</p>";
    }
}

/**
 * طباعة خطوة
 */
function printStep($step, $description) {
    global $isCommandLine;

    if ($isCommandLine) {
        echo "\n" . str_repeat("-", 50) . "\n";
        echo "الخطوة $step: $description\n";
        echo str_repeat("-", 50) . "\n";
    } else {
        echo "<div class='step'><h3>الخطوة $step: $description</h3>";
    }
}

/**
 * إنهاء خطوة
 */
function endStep() {
    global $isCommandLine;
    if (!$isCommandLine) {
        echo "</div>";
    }
}

// إعدادات قاعدة البيانات
$dbConfig = [
    'host' => 'localhost',
    'username' => 'root',
    'password' => '',
    'database' => 'efdcly_raqeeb',
    'charset' => 'utf8mb4'
];

try {
    printStep(1, "التحقق من الاتصال بخادم MySQL");

    // الاتصال بخادم MySQL بدون تحديد قاعدة بيانات
    $pdo = new PDO(
        "mysql:host={$dbConfig['host']};charset={$dbConfig['charset']}",
        $dbConfig['username'],
        $dbConfig['password'],
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES {$dbConfig['charset']} COLLATE {$dbConfig['charset']}_unicode_ci"
        ]
    );

    printMessage("تم الاتصال بخادم MySQL بنجاح", 'success');
    endStep();

    printStep(2, "إنشاء قاعدة البيانات");

    // التحقق من وجود قاعدة البيانات
    $stmt = $pdo->query("SHOW DATABASES LIKE '{$dbConfig['database']}'");
    if ($stmt->rowCount() > 0) {
        printMessage("قاعدة البيانات '{$dbConfig['database']}' موجودة بالفعل", 'warning');
        printMessage("سيتم حذف قاعدة البيانات الموجودة وإعادة إنشائها", 'warning');
        $pdo->exec("DROP DATABASE IF EXISTS `{$dbConfig['database']}`");
        printMessage("تم حذف قاعدة البيانات القديمة", 'info');
    }

    // إنشاء قاعدة البيانات
    $pdo->exec("CREATE DATABASE IF NOT EXISTS `{$dbConfig['database']}` CHARACTER SET {$dbConfig['charset']} COLLATE {$dbConfig['charset']}_unicode_ci");
    printMessage("تم إنشاء قاعدة البيانات '{$dbConfig['database']}' بنجاح", 'success');

    // الاتصال بقاعدة البيانات الجديدة
    $pdo->exec("USE `{$dbConfig['database']}`");
    endStep();

    printStep(3, "إنشاء جداول قاعدة البيانات");

    // قراءة وتنفيذ ملف schema.sql
    $schemaFile = __DIR__ . '/schema.sql';
    if (!file_exists($schemaFile)) {
        throw new Exception("ملف schema.sql غير موجود في: $schemaFile");
    }

    $schema = file_get_contents($schemaFile);
    if ($schema === false) {
        throw new Exception("فشل في قراءة ملف schema.sql");
    }

    // تقسيم الاستعلامات وتنفيذها
    $statements = array_filter(array_map('trim', explode(';', $schema)));
    $tableCount = 0;

    foreach ($statements as $statement) {
        if (empty($statement) || strpos($statement, '--') === 0) continue;

        try {
            $pdo->exec($statement);
            if (stripos($statement, 'CREATE TABLE') !== false) {
                $tableCount++;
                // استخراج اسم الجدول
                preg_match('/CREATE TABLE\s+`?(\w+)`?/i', $statement, $matches);
                if (isset($matches[1])) {
                    printMessage("تم إنشاء الجدول: {$matches[1]}", 'info');
                }
            }
        } catch (PDOException $e) {
            printMessage("خطأ في تنفيذ الاستعلام: " . $e->getMessage(), 'error');
        }
    }

    printMessage("تم إنشاء $tableCount جدول بنجاح", 'success');
    endStep();

    printStep(4, "إدراج البيانات الأولية");

    // قراءة وتنفيذ ملف seed_data.sql
    $seedFile = __DIR__ . '/seed_data.sql';
    if (!file_exists($seedFile)) {
        throw new Exception("ملف seed_data.sql غير موجود في: $seedFile");
    }

    $seedData = file_get_contents($seedFile);
    if ($seedData === false) {
        throw new Exception("فشل في قراءة ملف seed_data.sql");
    }

    // تقسيم الاستعلامات وتنفيذها
    $statements = array_filter(array_map('trim', explode(';', $seedData)));
    $insertCount = 0;

    foreach ($statements as $statement) {
        if (empty($statement) || strpos($statement, '--') === 0) continue;

        try {
            $result = $pdo->exec($statement);
            if (stripos($statement, 'INSERT') !== false && $result > 0) {
                $insertCount += $result;
            }
        } catch (PDOException $e) {
            printMessage("خطأ في إدراج البيانات: " . $e->getMessage(), 'warning');
        }
    }

    printMessage("تم إدراج $insertCount سجل من البيانات الأولية", 'success');
    endStep();

    printStep(5, "التحقق من سلامة قاعدة البيانات");

    // التحقق من الجداول الأساسية
    $requiredTables = ['users', 'roles', 'permissions', 'facilities', 'shipments', 'samples'];
    $missingTables = [];

    foreach ($requiredTables as $table) {
        $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
        if ($stmt->rowCount() == 0) {
            $missingTables[] = $table;
        }
    }

    if (empty($missingTables)) {
        printMessage("جميع الجداول الأساسية موجودة", 'success');
    } else {
        printMessage("الجداول المفقودة: " . implode(', ', $missingTables), 'error');
    }

    // التحقق من البيانات الأولية
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM roles");
    $roleCount = $stmt->fetch()['count'];

    $stmt = $pdo->query("SELECT COUNT(*) as count FROM permissions");
    $permissionCount = $stmt->fetch()['count'];

    printMessage("عدد الأدوار: $roleCount", 'info');
    printMessage("عدد الصلاحيات: $permissionCount", 'info');

    endStep();

    printStep(6, "إنشاء المستخدم المدير");

    // تضمين ملفات النظام
    require_once '../config/config.php';
    require_once '../modules/auth/User.php';

    $adminData = [
        'username' => 'admin',
        'email' => '<EMAIL>',
        'password' => 'admin123',
        'full_name' => 'مدير النظام العام',
        'phone' => '0912345678',
        'role_id' => 1,
        'is_active' => 1,
        'created_by' => null
    ];

    $userModel = new User();
    $existingUser = $userModel->findByUsername($adminData['username']);

    if ($existingUser) {
        printMessage("المستخدم المدير موجود بالفعل", 'warning');
    } else {
        $userId = $userModel->createUser($adminData);
        if ($userId) {
            printMessage("تم إنشاء المستخدم المدير بنجاح (ID: $userId)", 'success');
        } else {
            printMessage("فشل في إنشاء المستخدم المدير", 'error');
        }
    }

    endStep();

    // معلومات النهاية
    printMessage("🎉 تم إعداد قاعدة البيانات بنجاح!", 'success');
    printMessage("", 'info');
    printMessage("معلومات الدخول:", 'info');
    printMessage("اسم المستخدم: admin", 'info');
    printMessage("كلمة المرور: admin123", 'info');
    printMessage("رابط النظام: http://localhost/EFDCLY", 'info');

} catch (Exception $e) {
    printMessage("خطأ في إعداد قاعدة البيانات: " . $e->getMessage(), 'error');

    if (!$isCommandLine) {
        echo "<h4>خطوات استكشاف الأخطاء:</h4>";
        echo "<ol>";
        echo "<li>تأكد من تشغيل خادم MySQL</li>";
        echo "<li>تأكد من صحة بيانات الاتصال (المستخدم وكلمة المرور)</li>";
        echo "<li>تأكد من وجود صلاحيات إنشاء قواعد البيانات</li>";
        echo "<li>تأكد من وجود ملفات schema.sql و seed_data.sql</li>";
        echo "</ol>";
    }
}

if (!$isCommandLine) {
    echo "</div></body></html>";
}
?>