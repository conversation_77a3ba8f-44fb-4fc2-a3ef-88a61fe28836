<?php
/**
 * نظام الرقيب - إعداد قاعدة البيانات SQL Server
 * SQL Server Database Setup Script
 * Created: 2025-08-12
 */

// التحقق من تشغيل السكريبت من سطر الأوامر أو المتصفح
$isCommandLine = php_sapi_name() === 'cli';

if (!$isCommandLine) {
    echo "<!DOCTYPE html><html dir='rtl'><head><meta charset='UTF-8'><title>إعداد قاعدة البيانات SQL Server</title>";
    echo "<style>body{font-family:Arial;margin:40px;background:#f5f5f5;} .container{background:white;padding:30px;border-radius:10px;box-shadow:0 2px 10px rgba(0,0,0,0.1);} .success{color:#28a745;} .error{color:#dc3545;} .info{color:#17a2b8;} .warning{color:#ffc107;} .step{background:#f8f9fa;padding:15px;margin:10px 0;border-left:4px solid #007bff;}</style></head><body>";
    echo "<div class='container'><h1>🛡️ نظام الرقيب - إعداد قاعدة البيانات SQL Server</h1>";
}

/**
 * طباعة رسالة مع تنسيق مناسب للمتصفح أو سطر الأوامر
 */
function printMessage($message, $type = 'info') {
    global $isCommandLine;

    if ($isCommandLine) {
        $prefix = '';
        switch ($type) {
            case 'success': $prefix = '✅ '; break;
            case 'error': $prefix = '❌ '; break;
            case 'warning': $prefix = '⚠️ '; break;
            case 'info': $prefix = 'ℹ️ '; break;
        }
        echo $prefix . $message . "\n";
    } else {
        $class = $type;
        echo "<p class='$class'>$message</p>";
    }
}

/**
 * طباعة خطوة
 */
function printStep($step, $description) {
    global $isCommandLine;

    if ($isCommandLine) {
        echo "\n" . str_repeat("-", 50) . "\n";
        echo "الخطوة $step: $description\n";
        echo str_repeat("-", 50) . "\n";
    } else {
        echo "<div class='step'><h3>الخطوة $step: $description</h3>";
    }
}

/**
 * إنهاء خطوة
 */
function endStep() {
    global $isCommandLine;
    if (!$isCommandLine) {
        echo "</div>";
    }
}

// إعدادات قاعدة البيانات
$dbConfig = [
    'server' => 'YAKHLEF',
    'database' => 'efdcly_raqeeb',
    'username' => '', // فارغ للـ Windows Authentication
    'password' => '', // فارغ للـ Windows Authentication
    'port' => '1433'
];

try {
    printStep(1, "التحقق من إضافة SQL Server PDO");

    // التحقق من وجود إضافة SQL Server
    if (!extension_loaded('pdo_sqlsrv')) {
        throw new Exception("إضافة pdo_sqlsrv غير مثبتة. يرجى تثبيت Microsoft Drivers for PHP for SQL Server");
    }
    printMessage("إضافة pdo_sqlsrv متوفرة ✓", 'success');
    endStep();

    printStep(2, "الاتصال بخادم SQL Server");

    // الاتصال بخادم SQL Server بدون تحديد قاعدة بيانات
    if (empty($dbConfig['username']) && empty($dbConfig['password'])) {
        // استخدام Windows Authentication
        $dsn = "sqlsrv:Server={$dbConfig['server']},{$dbConfig['port']};TrustServerCertificate=true";
        $pdo = new PDO($dsn, null, null, [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::SQLSRV_ATTR_ENCODING => PDO::SQLSRV_ENCODING_UTF8
        ]);
        printMessage("تم الاتصال باستخدام Windows Authentication", 'success');
    } else {
        // استخدام SQL Server Authentication
        $dsn = "sqlsrv:Server={$dbConfig['server']},{$dbConfig['port']};TrustServerCertificate=true";
        $pdo = new PDO($dsn, $dbConfig['username'], $dbConfig['password'], [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::SQLSRV_ATTR_ENCODING => PDO::SQLSRV_ENCODING_UTF8
        ]);
        printMessage("تم الاتصال باستخدام SQL Server Authentication", 'success');
    }

    printMessage("تم الاتصال بخادم SQL Server بنجاح", 'success');
    endStep();

    printStep(3, "إنشاء قاعدة البيانات");

    // التحقق من وجود قاعدة البيانات
    $stmt = $pdo->query("SELECT name FROM sys.databases WHERE name = '{$dbConfig['database']}'");
    if ($stmt->rowCount() > 0) {
        printMessage("قاعدة البيانات '{$dbConfig['database']}' موجودة بالفعل", 'warning');
        printMessage("سيتم حذف قاعدة البيانات الموجودة وإعادة إنشائها", 'warning');

        // قطع جميع الاتصالات النشطة
        $pdo->exec("ALTER DATABASE [{$dbConfig['database']}] SET SINGLE_USER WITH ROLLBACK IMMEDIATE");
        $pdo->exec("DROP DATABASE [{$dbConfig['database']}]");
        printMessage("تم حذف قاعدة البيانات القديمة", 'info');
    }

    // إنشاء قاعدة البيانات
    $pdo->exec("CREATE DATABASE [{$dbConfig['database']}] COLLATE Arabic_CI_AS");
    printMessage("تم إنشاء قاعدة البيانات '{$dbConfig['database']}' بنجاح", 'success');

    // الاتصال بقاعدة البيانات الجديدة
    if (empty($dbConfig['username']) && empty($dbConfig['password'])) {
        $dsn = "sqlsrv:Server={$dbConfig['server']},{$dbConfig['port']};Database={$dbConfig['database']};TrustServerCertificate=true";
        $pdo = new PDO($dsn, null, null, [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::SQLSRV_ATTR_ENCODING => PDO::SQLSRV_ENCODING_UTF8
        ]);
    } else {
        $dsn = "sqlsrv:Server={$dbConfig['server']},{$dbConfig['port']};Database={$dbConfig['database']};TrustServerCertificate=true";
        $pdo = new PDO($dsn, $dbConfig['username'], $dbConfig['password'], [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::SQLSRV_ATTR_ENCODING => PDO::SQLSRV_ENCODING_UTF8
        ]);
    }

    endStep();

    printStep(4, "إنشاء جداول قاعدة البيانات");

    // قراءة وتنفيذ ملف schema_sqlserver.sql
    $schemaFile = __DIR__ . '/schema_sqlserver.sql';
    if (!file_exists($schemaFile)) {
        throw new Exception("ملف schema_sqlserver.sql غير موجود في: $schemaFile");
    }

    $schema = file_get_contents($schemaFile);
    if ($schema === false) {
        throw new Exception("فشل في قراءة ملف schema_sqlserver.sql");
    }

    // تقسيم الاستعلامات وتنفيذها
    $statements = array_filter(array_map('trim', preg_split('/;\s*(?=CREATE|INSERT|ALTER)/i', $schema)));
    $tableCount = 0;

    foreach ($statements as $statement) {
        if (empty($statement) || strpos($statement, '--') === 0) continue;

        try {
            $pdo->exec($statement);
            if (stripos($statement, 'CREATE TABLE') !== false) {
                $tableCount++;
                // استخراج اسم الجدول
                preg_match('/CREATE TABLE\s+(\w+)/i', $statement, $matches);
                if (isset($matches[1])) {
                    printMessage("تم إنشاء الجدول: {$matches[1]}", 'info');
                }
            }
        } catch (PDOException $e) {
            printMessage("خطأ في تنفيذ الاستعلام: " . $e->getMessage(), 'error');
        }
    }

    printMessage("تم إنشاء $tableCount جدول بنجاح", 'success');
    endStep();

    printStep(5, "إدراج البيانات الأولية");

    // قراءة وتنفيذ ملف seed_data_sqlserver.sql
    $seedFile = __DIR__ . '/seed_data_sqlserver.sql';
    if (!file_exists($seedFile)) {
        throw new Exception("ملف seed_data_sqlserver.sql غير موجود في: $seedFile");
    }

    $seedData = file_get_contents($seedFile);
    if ($seedData === false) {
        throw new Exception("فشل في قراءة ملف seed_data_sqlserver.sql");
    }

    // تقسيم الاستعلامات وتنفيذها
    $statements = array_filter(array_map('trim', preg_split('/;\s*(?=INSERT)/i', $seedData)));
    $insertCount = 0;

    foreach ($statements as $statement) {
        if (empty($statement) || strpos($statement, '--') === 0) continue;

        try {
            $result = $pdo->exec($statement);
            if (stripos($statement, 'INSERT') !== false && $result > 0) {
                $insertCount += $result;
            }
        } catch (PDOException $e) {
            printMessage("خطأ في إدراج البيانات: " . $e->getMessage(), 'warning');
        }
    }

    printMessage("تم إدراج $insertCount سجل من البيانات الأولية", 'success');
    endStep();

    printStep(6, "إنشاء المستخدم المدير");

    // تضمين ملفات النظام
    require_once '../config/config.php';
    require_once '../modules/auth/User.php';

    $adminData = [
        'username' => 'admin',
        'email' => '<EMAIL>',
        'password' => 'admin123',
        'full_name' => 'مدير النظام العام',
        'phone' => '0912345678',
        'role_id' => 1,
        'is_active' => 1,
        'created_by' => null
    ];

    $userModel = new User();
    $existingUser = $userModel->findByUsername($adminData['username']);

    if ($existingUser) {
        printMessage("المستخدم المدير موجود بالفعل", 'warning');
    } else {
        $userId = $userModel->createUser($adminData);
        if ($userId) {
            printMessage("تم إنشاء المستخدم المدير بنجاح (ID: $userId)", 'success');
        } else {
            printMessage("فشل في إنشاء المستخدم المدير", 'error');
        }
    }

    endStep();

    // معلومات النهاية
    printMessage("🎉 تم إعداد قاعدة البيانات SQL Server بنجاح!", 'success');
    printMessage("", 'info');
    printMessage("معلومات الدخول:", 'info');
    printMessage("اسم المستخدم: admin", 'info');
    printMessage("كلمة المرور: admin123", 'info');
    printMessage("رابط النظام: http://localhost/EFDCLY", 'info');
    printMessage("خادم قاعدة البيانات: {$dbConfig['server']}", 'info');
    printMessage("اسم قاعدة البيانات: {$dbConfig['database']}", 'info');

} catch (Exception $e) {
    printMessage("خطأ في إعداد قاعدة البيانات: " . $e->getMessage(), 'error');

    if (!$isCommandLine) {
        echo "<h4>خطوات استكشاف الأخطاء:</h4>";
        echo "<ol>";
        echo "<li>تأكد من تشغيل خدمة SQL Server</li>";
        echo "<li>تأكد من صحة اسم الخادم: YAKHLEF</li>";
        echo "<li>تأكد من تثبيت Microsoft Drivers for PHP for SQL Server</li>";
        echo "<li>تأكد من وجود صلاحيات إنشاء قواعد البيانات</li>";
        echo "<li>تأكد من وجود ملفات schema_sqlserver.sql و seed_data_sqlserver.sql</li>";
        echo "</ol>";

        echo "<h4>روابط مفيدة:</h4>";
        echo "<ul>";
        echo "<li><a href='https://docs.microsoft.com/en-us/sql/connect/php/download-drivers-php-sql-server'>تحميل Microsoft Drivers for PHP for SQL Server</a></li>";
        echo "<li><a href='https://docs.microsoft.com/en-us/sql/database-engine/configure-windows/configure-a-server-to-listen-on-a-specific-tcp-port'>إعداد SQL Server للاستماع على منفذ TCP</a></li>";
        echo "</ul>";
    }
}

if (!$isCommandLine) {
    echo "</div></body></html>";
}
?>