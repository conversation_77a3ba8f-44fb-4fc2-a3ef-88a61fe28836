<?php
/**
 * نظام الرقيب - دليل تثبيت Microsoft Drivers for PHP for SQL Server
 * SQL Server Drivers Installation Guide
 * Created: 2025-08-12
 */

// التحقق من تشغيل السكريبت من سطر الأوامر أو المتصفح
$isCommandLine = php_sapi_name() === 'cli';

if (!$isCommandLine) {
    echo "<!DOCTYPE html><html dir='rtl'><head><meta charset='UTF-8'><title>دليل تثبيت SQL Server Drivers</title>";
    echo "<style>body{font-family:Arial;margin:40px;background:#f5f5f5;} .container{background:white;padding:30px;border-radius:10px;box-shadow:0 2px 10px rgba(0,0,0,0.1);} .success{color:#28a745;} .error{color:#dc3545;} .info{color:#17a2b8;} .warning{color:#ffc107;} .step{background:#f8f9fa;padding:15px;margin:10px 0;border-left:4px solid #007bff;border-radius:5px;} .code{background:#f8f8f8;padding:10px;border-radius:5px;font-family:monospace;margin:10px 0;} .btn{background:#007bff;color:white;padding:10px 20px;border:none;border-radius:5px;text-decoration:none;display:inline-block;margin:5px;} .btn:hover{background:#0056b3;}</style></head><body>";
    echo "<div class='container'><h1>🛡️ نظام الرقيب - دليل تثبيت Microsoft Drivers for PHP for SQL Server</h1>";
}

// فحص الحالة الحالية
$phpVersion = phpversion();
$phpArch = (PHP_INT_SIZE === 8) ? 'x64' : 'x86';
$isThreadSafe = (defined('ZEND_THREAD_SAFE') && ZEND_THREAD_SAFE) ? 'TS' : 'NTS';

if (!$isCommandLine) {
    echo "<h2>🔍 فحص النظام الحالي</h2>";

    echo "<div class='step'>";
    echo "<h3>معلومات PHP:</h3>";
    echo "<ul>";
    echo "<li><strong>إصدار PHP:</strong> $phpVersion</li>";
    echo "<li><strong>معمارية النظام:</strong> $phpArch</li>";
    echo "<li><strong>Thread Safety:</strong> $isThreadSafe</li>";
    echo "<li><strong>مجلد الإضافات:</strong> " . ini_get('extension_dir') . "</li>";
    echo "</ul>";
    echo "</div>";

    // فحص الإضافات الحالية
    echo "<div class='step'>";
    echo "<h3>حالة الإضافات:</h3>";

    if (extension_loaded('pdo')) {
        echo "<div class='success'>✅ PDO متوفر</div>";
    } else {
        echo "<div class='error'>❌ PDO غير متوفر</div>";
    }

    if (extension_loaded('pdo_mysql')) {
        echo "<div class='success'>✅ MySQL PDO متوفر</div>";
    } else {
        echo "<div class='warning'>⚠️ MySQL PDO غير متوفر</div>";
    }

    if (extension_loaded('pdo_sqlsrv')) {
        echo "<div class='success'>✅ SQL Server PDO متوفر - لا حاجة للتثبيت!</div>";
        echo "<p><a href='switch_database.php' class='btn'>الذهاب لأداة التبديل</a></p>";
    } else {
        echo "<div class='error'>❌ SQL Server PDO غير متوفر - يحتاج تثبيت</div>";
    }

    if (extension_loaded('sqlsrv')) {
        echo "<div class='success'>✅ SQL Server Extension متوفر</div>";
    } else {
        echo "<div class='warning'>⚠️ SQL Server Extension غير متوفر</div>";
    }
    echo "</div>";

    // دليل التثبيت
    echo "<h2>📥 دليل التثبيت خطوة بخطوة</h2>";

    echo "<div class='step'>";
    echo "<h3>الخطوة 1: تحديد الإصدار المطلوب</h3>";
    echo "<p>بناءً على نظامك الحالي، تحتاج إلى:</p>";
    echo "<div class='code'>";
    echo "PHP Version: $phpVersion<br>";
    echo "Architecture: $phpArch<br>";
    echo "Thread Safety: $isThreadSafe<br>";
    echo "<strong>الملف المطلوب: php_pdo_sqlsrv_" . substr($phpVersion, 0, 3) . "_$isThreadSafe" . "_$phpArch.dll</strong>";
    echo "</div>";
    echo "</div>";

    echo "<div class='step'>";
    echo "<h3>الخطوة 2: تحميل Microsoft Drivers</h3>";
    echo "<p>اذهب إلى الرابط الرسمي:</p>";
    echo "<div class='code'>";
    echo "<a href='https://docs.microsoft.com/en-us/sql/connect/php/download-drivers-php-sql-server' target='_blank'>";
    echo "https://docs.microsoft.com/en-us/sql/connect/php/download-drivers-php-sql-server";
    echo "</a>";
    echo "</div>";
    echo "<p>أو استخدم الرابط المباشر:</p>";
    echo "<div class='code'>";
    echo "<a href='https://github.com/Microsoft/msphpsql/releases' target='_blank'>";
    echo "https://github.com/Microsoft/msphpsql/releases";
    echo "</a>";
    echo "</div>";
    echo "</div>";

    echo "<div class='step'>";
    echo "<h3>الخطوة 3: استخراج الملفات</h3>";
    echo "<ol>";
    echo "<li>حمل ملف <code>SQLSRV-5.x.x-Windows.zip</code> (أحدث إصدار)</li>";
    echo "<li>استخرج الملفات إلى مجلد مؤقت</li>";
    echo "<li>ابحث عن الملفات التالية:</li>";
    echo "<ul>";
    echo "<li><code>php_pdo_sqlsrv_" . substr($phpVersion, 0, 3) . "_$isThreadSafe" . "_$phpArch.dll</code></li>";
    echo "<li><code>php_sqlsrv_" . substr($phpVersion, 0, 3) . "_$isThreadSafe" . "_$phpArch.dll</code></li>";
    echo "</ul>";
    echo "</ol>";
    echo "</div>";

    echo "<div class='step'>";
    echo "<h3>الخطوة 4: نسخ الملفات</h3>";
    echo "<p>انسخ الملفات إلى مجلد إضافات PHP:</p>";
    echo "<div class='code'>";
    echo "المجلد المطلوب: " . ini_get('extension_dir');
    echo "</div>";
    echo "<p>عادة يكون:</p>";
    echo "<div class='code'>";
    echo "C:\\xampp\\php\\ext\\";
    echo "</div>";
    echo "</div>";

    echo "<div class='step'>";
    echo "<h3>الخطوة 5: تعديل ملف php.ini</h3>";
    echo "<p>أضف الأسطر التالية إلى ملف php.ini:</p>";
    echo "<div class='code'>";
    echo "extension=pdo_sqlsrv<br>";
    echo "extension=sqlsrv";
    echo "</div>";
    echo "<p>مكان ملف php.ini:</p>";
    echo "<div class='code'>";
    echo php_ini_loaded_file();
    echo "</div>";
    echo "</div>";

    echo "<div class='step'>";
    echo "<h3>الخطوة 6: إعادة تشغيل Apache</h3>";
    echo "<ol>";
    echo "<li>أوقف Apache في XAMPP Control Panel</li>";
    echo "<li>انتظر 5 ثوانِ</li>";
    echo "<li>شغل Apache مرة أخرى</li>";
    echo "<li>تحقق من أن Apache يعمل بدون أخطاء</li>";
    echo "</ol>";
    echo "</div>";

    echo "<div class='step'>";
    echo "<h3>الخطوة 7: التحقق من التثبيت</h3>";
    echo "<p>بعد إعادة تشغيل Apache:</p>";
    echo "<ol>";
    echo "<li><a href='?' class='btn'>أعد تحميل هذه الصفحة</a></li>";
    echo "<li>تحقق من ظهور ✅ بجانب SQL Server PDO</li>";
    echo "<li>أو استخدم: <a href='check_sqlserver.php' class='btn'>أداة فحص SQL Server</a></li>";
    echo "</ol>";
    echo "</div>";

    echo "</div></body></html>";
}
?>