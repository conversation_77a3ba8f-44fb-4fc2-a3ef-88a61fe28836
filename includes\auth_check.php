<?php
/**
 * نظام الرقيب - فحص المصادقة والصلاحيات
 * Authentication and Authorization Check
 * Created: 2025-08-12
 */

// التأكد من تضمين ملف الإعدادات
if (!defined('BASE_PATH')) {
    require_once '../config/config.php';
}

/**
 * فئة فحص المصادقة
 * Authentication Check Class
 */
class AuthCheck {

    /**
     * التحقق من تسجيل الدخول
     * Check if user is logged in
     */
    public static function isLoggedIn() {
        return isset($_SESSION['user_id']) && !empty($_SESSION['user_id']);
    }

    /**
     * التحقق من انتهاء صلاحية الجلسة
     * Check session timeout
     */
    public static function checkSessionTimeout() {
        if (isset($_SESSION['last_activity'])) {
            $inactive = time() - $_SESSION['last_activity'];
            if ($inactive >= SESSION_TIMEOUT) {
                self::logout();
                return false;
            }
        }
        $_SESSION['last_activity'] = time();
        return true;
    }

    /**
     * فرض تسجيل الدخول
     * Require login
     */
    public static function requireLogin() {
        if (!self::isLoggedIn()) {
            Helper::redirect(BASE_URL . '/modules/auth/login.php');
        }

        if (!self::checkSessionTimeout()) {
            Helper::redirect(BASE_URL . '/modules/auth/login.php?timeout=1');
        }
    }

    /**
     * التحقق من الصلاحية
     * Check permission
     */
    public static function hasPermission($permission) {
        if (!self::isLoggedIn()) {
            return false;
        }

        require_once BASE_PATH . '/modules/auth/User.php';
        $userModel = new User();
        return $userModel->hasPermission($_SESSION['user_id'], $permission);
    }

    /**
     * فرض وجود صلاحية معينة
     * Require specific permission
     */
    public static function requirePermission($permission) {
        self::requireLogin();

        if (!self::hasPermission($permission)) {
            http_response_code(403);
            die('ليس لديك صلاحية للوصول إلى هذه الصفحة.');
        }
    }

    /**
     * الحصول على المستخدم الحالي
     * Get current user
     */
    public static function getCurrentUser() {
        if (!self::isLoggedIn()) {
            return null;
        }

        require_once BASE_PATH . '/modules/auth/User.php';
        $userModel = new User();
        return $userModel->getUserWithRole($_SESSION['user_id']);
    }

    /**
     * الحصول على صلاحيات المستخدم الحالي
     * Get current user permissions
     */
    public static function getCurrentUserPermissions() {
        if (!self::isLoggedIn()) {
            return [];
        }

        require_once BASE_PATH . '/modules/auth/User.php';
        $userModel = new User();
        return $userModel->getUserPermissions($_SESSION['user_id']);
    }

    /**
     * تسجيل الخروج
     * Logout
     */
    public static function logout() {
        session_unset();
        session_destroy();
    }

    /**
     * التحقق من الدور
     * Check role
     */
    public static function hasRole($roleName) {
        if (!self::isLoggedIn()) {
            return false;
        }

        $user = self::getCurrentUser();
        return $user && $user['role_name'] === $roleName;
    }

    /**
     * فرض وجود دور معين
     * Require specific role
     */
    public static function requireRole($roleName) {
        self::requireLogin();

        if (!self::hasRole($roleName)) {
            http_response_code(403);
            die('ليس لديك صلاحية للوصول إلى هذه الصفحة.');
        }
    }

    /**
     * التحقق من كون المستخدم مدير
     * Check if user is admin
     */
    public static function isAdmin() {
        return self::hasRole('admin') || self::hasRole('super_admin');
    }

    /**
     * فرض كون المستخدم مدير
     * Require admin role
     */
    public static function requireAdmin() {
        self::requireLogin();

        if (!self::isAdmin()) {
            http_response_code(403);
            die('هذه الصفحة مخصصة للمديرين فقط.');
        }
    }
}

/**
 * دوال مساعدة سريعة
 * Quick helper functions
 */

/**
 * التحقق من تسجيل الدخول
 */
function isLoggedIn() {
    return AuthCheck::isLoggedIn();
}

/**
 * فرض تسجيل الدخول
 */
function requireLogin() {
    AuthCheck::requireLogin();
}

/**
 * التحقق من الصلاحية
 */
function hasPermission($permission) {
    return AuthCheck::hasPermission($permission);
}

/**
 * فرض وجود صلاحية
 */
function requirePermission($permission) {
    AuthCheck::requirePermission($permission);
}

/**
 * الحصول على المستخدم الحالي
 */
function getCurrentUser() {
    return AuthCheck::getCurrentUser();
}

/**
 * التحقق من كون المستخدم مدير
 */
function isAdmin() {
    return AuthCheck::isAdmin();
}

/**
 * فرض كون المستخدم مدير
 */
function requireAdmin() {
    AuthCheck::requireAdmin();
}
?>