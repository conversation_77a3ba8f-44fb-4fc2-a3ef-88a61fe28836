<?php
/**
 * نظام الرقيب - الإعدادات العامة
 * General Configuration for Registration & Tracking System
 * Created: 2025-08-12
 */

// بدء الجلسة
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// إعدادات النظام العامة
define('SYSTEM_NAME', 'نظام الرقيب');
define('SYSTEM_VERSION', '1.0.0');
define('SYSTEM_DESCRIPTION', 'نظام تسجيل ومتابعة شركات استيراد وتصدير الأدوية والمواد الغذائية');

// إعدادات المسارات
define('BASE_PATH', dirname(__DIR__));
define('BASE_URL', 'http://localhost/EFDCLY');
define('ASSETS_URL', BASE_URL . '/assets');
define('UPLOADS_PATH', BASE_PATH . '/public/uploads');
define('UPLOADS_URL', BASE_URL . '/public/uploads');

// إعدادات الأمان
define('HASH_ALGORITHM', 'sha256');
define('SESSION_TIMEOUT', 3600); // ساعة واحدة
define('MAX_LOGIN_ATTEMPTS', 5);
define('LOGIN_LOCKOUT_TIME', 900); // 15 دقيقة

// إعدادات الملفات
define('MAX_FILE_SIZE', 10 * 1024 * 1024); // 10 ميجابايت
define('ALLOWED_FILE_TYPES', ['pdf', 'doc', 'docx', 'jpg', 'jpeg', 'png', 'gif']);

// إعدادات التاريخ والوقت
date_default_timezone_set('Africa/Tripoli');
define('DATE_FORMAT', 'Y-m-d');
define('DATETIME_FORMAT', 'Y-m-d H:i:s');
define('DISPLAY_DATE_FORMAT', 'd/m/Y');
define('DISPLAY_DATETIME_FORMAT', 'd/m/Y H:i');

// إعدادات اللغة
define('DEFAULT_LANGUAGE', 'ar');
define('SUPPORTED_LANGUAGES', ['ar', 'en']);

// إعدادات البريد الإلكتروني
define('SMTP_HOST', 'localhost');
define('SMTP_PORT', 587);
define('SMTP_USERNAME', '');
define('SMTP_PASSWORD', '');
define('SMTP_FROM_EMAIL', '<EMAIL>');
define('SMTP_FROM_NAME', 'نظام الرقيب');

// إعدادات الإشعارات
define('ENABLE_EMAIL_NOTIFICATIONS', true);
define('ENABLE_SMS_NOTIFICATIONS', false);

// تضمين ملفات الإعدادات الأخرى
require_once BASE_PATH . '/config/database.php';

/**
 * فئة الإعدادات العامة
 * General Settings Class
 */
class Config {
    private static $settings = [];

    /**
     * تحميل الإعدادات من قاعدة البيانات
     * Load settings from database
     */
    public static function loadSettings() {
        try {
            $db = Database::getInstance()->getConnection();
            $stmt = $db->query("SELECT setting_key, setting_value, setting_type FROM system_settings");
            $settings = $stmt->fetchAll();

            foreach ($settings as $setting) {
                $value = $setting['setting_value'];

                // تحويل القيمة حسب النوع
                switch ($setting['setting_type']) {
                    case 'boolean':
                        $value = filter_var($value, FILTER_VALIDATE_BOOLEAN);
                        break;
                    case 'number':
                        $value = is_numeric($value) ? (float)$value : 0;
                        break;
                    case 'json':
                        $value = json_decode($value, true);
                        break;
                }

                self::$settings[$setting['setting_key']] = $value;
            }
        } catch (Exception $e) {
            // في حالة عدم وجود قاعدة البيانات أو جدول الإعدادات
            error_log("خطأ في تحميل الإعدادات: " . $e->getMessage());
        }
    }

    /**
     * الحصول على قيمة إعداد
     * Get setting value
     */
    public static function get($key, $default = null) {
        return isset(self::$settings[$key]) ? self::$settings[$key] : $default;
    }

    /**
     * تعيين قيمة إعداد
     * Set setting value
     */
    public static function set($key, $value) {
        self::$settings[$key] = $value;
    }

    /**
     * الحصول على جميع الإعدادات
     * Get all settings
     */
    public static function getAll() {
        return self::$settings;
    }
}

/**
 * فئة المساعدات العامة
 * General Helper Functions
 */
class Helper {
    /**
     * تنظيف البيانات المدخلة
     * Sanitize input data
     */
    public static function sanitize($data) {
        if (is_array($data)) {
            return array_map([self::class, 'sanitize'], $data);
        }
        return htmlspecialchars(trim($data), ENT_QUOTES, 'UTF-8');
    }

    /**
     * التحقق من صحة البريد الإلكتروني
     * Validate email address
     */
    public static function validateEmail($email) {
        return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
    }

    /**
     * تشفير كلمة المرور
     * Hash password
     */
    public static function hashPassword($password) {
        return password_hash($password, PASSWORD_DEFAULT);
    }

    /**
     * التحقق من كلمة المرور
     * Verify password
     */
    public static function verifyPassword($password, $hash) {
        return password_verify($password, $hash);
    }

    /**
     * توليد رمز عشوائي
     * Generate random token
     */
    public static function generateToken($length = 32) {
        return bin2hex(random_bytes($length / 2));
    }

    /**
     * تنسيق التاريخ للعرض
     * Format date for display
     */
    public static function formatDate($date, $format = DISPLAY_DATE_FORMAT) {
        if (empty($date) || $date === '0000-00-00') {
            return '';
        }
        return date($format, strtotime($date));
    }

    /**
     * تنسيق التاريخ والوقت للعرض
     * Format datetime for display
     */
    public static function formatDateTime($datetime, $format = DISPLAY_DATETIME_FORMAT) {
        if (empty($datetime) || $datetime === '0000-00-00 00:00:00') {
            return '';
        }
        return date($format, strtotime($datetime));
    }

    /**
     * تحويل الحجم بالبايت إلى وحدة قابلة للقراءة
     * Convert bytes to human readable format
     */
    public static function formatBytes($bytes, $precision = 2) {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];

        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }

        return round($bytes, $precision) . ' ' . $units[$i];
    }

    /**
     * إعادة التوجيه
     * Redirect to URL
     */
    public static function redirect($url) {
        header("Location: $url");
        exit;
    }

    /**
     * عرض رسالة JSON
     * Output JSON response
     */
    public static function jsonResponse($data, $status = 200) {
        http_response_code($status);
        header('Content-Type: application/json; charset=utf-8');
        echo json_encode($data, JSON_UNESCAPED_UNICODE);
        exit;
    }
}

// تحميل الإعدادات عند تضمين الملف
Config::loadSettings();
?>