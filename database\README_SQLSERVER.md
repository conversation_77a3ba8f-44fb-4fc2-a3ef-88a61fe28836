# حل مشكلة SQL Server PDO في نظام الرقيب

## 🚨 المشكلة
```
Fatal error: Undefined constant PDO::SQLSRV_ATTR_ENCODING
```

## ✅ الحل السريع

### الخيار الأول: استخدام MySQL (الأسهل)
```bash
# 1. اذهب إلى أداة التبديل
http://localhost/EFDCLY/database/switch_database.php

# 2. اختر "التبديل إلى MySQL"

# 3. اذهب إلى إعداد MySQL
http://localhost/EFDCLY/database/setup_database.php
```

### الخيار الثاني: تثبيت SQL Server Drivers
```bash
# 1. اذهب إلى دليل التثبيت
http://localhost/EFDCLY/database/install_sqlserver_drivers.php

# 2. اتبع التعليمات خطوة بخطوة

# 3. بعد التثبيت، اذهب إلى:
http://localhost/EFDCLY/database/setup_sqlserver.php
```

## 📋 خطوات تثبيت SQL Server Drivers (مختصرة)

### 1. تحديد المطلوب
- افتح: `http://localhost/EFDCLY/database/install_sqlserver_drivers.php`
- اكتب اسم الملف المطلوب (سيظهر لك)

### 2. التحميل
- اذهب إلى: https://github.com/Microsoft/msphpsql/releases
- حمل أحدث إصدار `SQLSRV-5.x.x-Windows.zip`

### 3. النسخ
```bash
# استخرج الملفات وانسخ:
php_pdo_sqlsrv_XX_XX_XX.dll
php_sqlsrv_XX_XX_XX.dll

# إلى مجلد:
C:\xampp\php\ext\
```

### 4. تعديل php.ini
```ini
# أضف هذين السطرين إلى ملف php.ini:
extension=pdo_sqlsrv
extension=sqlsrv
```

### 5. إعادة تشغيل Apache
- أوقف Apache في XAMPP
- شغله مرة أخرى

### 6. التحقق
```bash
http://localhost/EFDCLY/database/install_sqlserver_drivers.php
```

## 🔗 روابط مفيدة

- **أداة التبديل**: `database/switch_database.php`
- **دليل التثبيت**: `database/install_sqlserver_drivers.php`
- **إعداد MySQL**: `database/setup_database.php`
- **إعداد SQL Server**: `database/setup_sqlserver.php`
- **فحص MySQL**: `database/check_system.php`
- **فحص SQL Server**: `database/check_sqlserver.php`

## 💡 نصائح

1. **للمبتدئين**: استخدم MySQL (أسهل)
2. **للمتقدمين**: ثبت SQL Server Drivers
3. **في حالة المشاكل**: راجع دليل التثبيت المفصل
4. **للتأكد**: استخدم أدوات الفحص

## 🆘 إذا لم يعمل شيء

1. تأكد من تشغيل Apache في XAMPP
2. تأكد من أن MySQL يعمل (للخيار الأول)
3. راجع ملف error.log في XAMPP
4. جرب إعادة تشغيل XAMPP بالكامل