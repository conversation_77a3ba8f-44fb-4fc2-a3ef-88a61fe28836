<?php
/**
 * نظام الرقيب - نموذج المستخدم
 * User Model for Registration & Tracking System
 * Created: 2025-08-12
 */

require_once '../../config/config.php';

/**
 * فئة المستخدم
 * User Class
 */
class User extends BaseModel {
    protected $table = 'users';

    /**
     * البحث عن مستخدم بواسطة اسم المستخدم
     * Find user by username
     */
    public function findByUsername($username) {
        $stmt = $this->db->prepare("SELECT * FROM {$this->table} WHERE username = ? AND is_active = 1");
        $stmt->execute([$username]);
        return $stmt->fetch();
    }

    /**
     * البحث عن مستخدم بواسطة البريد الإلكتروني
     * Find user by email
     */
    public function findByEmail($email) {
        $stmt = $this->db->prepare("SELECT * FROM {$this->table} WHERE email = ? AND is_active = 1");
        $stmt->execute([$email]);
        return $stmt->fetch();
    }

    /**
     * التحقق من بيانات تسجيل الدخول
     * Authenticate user credentials
     */
    public function authenticate($username, $password) {
        $user = $this->findByUsername($username);

        if ($user && Helper::verifyPassword($password, $user['password_hash'])) {
            // تحديث آخر تسجيل دخول
            $this->updateLastLogin($user['id']);
            return $user;
        }

        return false;
    }

    /**
     * تحديث آخر تسجيل دخول
     * Update last login timestamp
     */
    public function updateLastLogin($userId) {
        $stmt = $this->db->prepare("UPDATE {$this->table} SET last_login = NOW() WHERE id = ?");
        return $stmt->execute([$userId]);
    }

    /**
     * إنشاء مستخدم جديد
     * Create new user
     */
    public function createUser($data) {
        // تشفير كلمة المرور
        if (isset($data['password'])) {
            $data['password_hash'] = Helper::hashPassword($data['password']);
            unset($data['password']);
        }

        // إضافة تاريخ الإنشاء
        $data['created_at'] = date(DATETIME_FORMAT);

        return $this->create($data);
    }

    /**
     * تحديث بيانات المستخدم
     * Update user data
     */
    public function updateUser($id, $data) {
        // تشفير كلمة المرور إذا تم تغييرها
        if (isset($data['password']) && !empty($data['password'])) {
            $data['password_hash'] = Helper::hashPassword($data['password']);
            unset($data['password']);
        } else {
            unset($data['password']);
        }

        // إضافة تاريخ التحديث
        $data['updated_at'] = date(DATETIME_FORMAT);

        return $this->update($id, $data);
    }

    /**
     * الحصول على صلاحيات المستخدم
     * Get user permissions
     */
    public function getUserPermissions($userId) {
        $sql = "
            SELECT DISTINCT p.name, p.name_ar, p.module
            FROM permissions p
            INNER JOIN role_permissions rp ON p.id = rp.permission_id
            INNER JOIN users u ON rp.role_id = u.role_id
            WHERE u.id = ? AND u.is_active = 1
        ";

        $stmt = $this->db->prepare($sql);
        $stmt->execute([$userId]);
        return $stmt->fetchAll();
    }

    /**
     * التحقق من وجود صلاحية معينة للمستخدم
     * Check if user has specific permission
     */
    public function hasPermission($userId, $permission) {
        $sql = "
            SELECT COUNT(*)
            FROM permissions p
            INNER JOIN role_permissions rp ON p.id = rp.permission_id
            INNER JOIN users u ON rp.role_id = u.role_id
            WHERE u.id = ? AND p.name = ? AND u.is_active = 1
        ";

        $stmt = $this->db->prepare($sql);
        $stmt->execute([$userId, $permission]);
        return $stmt->fetchColumn() > 0;
    }

    /**
     * الحصول على بيانات المستخدم مع الدور
     * Get user data with role information
     */
    public function getUserWithRole($userId) {
        $sql = "
            SELECT u.*, r.name as role_name, r.name_ar as role_name_ar
            FROM {$this->table} u
            LEFT JOIN roles r ON u.role_id = r.id
            WHERE u.id = ?
        ";

        $stmt = $this->db->prepare($sql);
        $stmt->execute([$userId]);
        return $stmt->fetch();
    }

    /**
     * الحصول على جميع المستخدمين مع أدوارهم
     * Get all users with their roles
     */
    public function getAllUsersWithRoles($limit = null, $offset = 0) {
        $sql = "
            SELECT u.*, r.name as role_name, r.name_ar as role_name_ar
            FROM {$this->table} u
            LEFT JOIN roles r ON u.role_id = r.id
            ORDER BY u.created_at DESC
        ";

        if ($limit) {
            $sql .= " LIMIT $limit OFFSET $offset";
        }

        $stmt = $this->db->prepare($sql);
        $stmt->execute();
        return $stmt->fetchAll();
    }

    /**
     * البحث في المستخدمين
     * Search users
     */
    public function searchUsers($searchTerm, $limit = 20) {
        $sql = "
            SELECT u.*, r.name as role_name, r.name_ar as role_name_ar
            FROM {$this->table} u
            LEFT JOIN roles r ON u.role_id = r.id
            WHERE (u.full_name LIKE ? OR u.username LIKE ? OR u.email LIKE ?)
            ORDER BY u.full_name
            LIMIT ?
        ";

        $searchPattern = "%$searchTerm%";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$searchPattern, $searchPattern, $searchPattern, $limit]);
        return $stmt->fetchAll();
    }
}
?>