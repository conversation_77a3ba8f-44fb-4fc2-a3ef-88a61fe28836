# دليل التثبيت السريع - نظام الرقيب

## 🚀 التثبيت السريع (5 دقائق)

### المتطلبات الأساسية
- XAMPP أو WAMP مع PHP 8.0+ و MySQL 8.0+
- متصفح حديث يدعم JavaScript

### خطوات التثبيت

#### 1. تحضير البيئة
```bash
# تأكد من تشغيل Apache و MySQL في XAMPP
# انسخ مجلد المشروع إلى htdocs
cp -r EFDCLY/ C:/xampp/htdocs/
```

#### 2. إعداد قاعدة البيانات (طريقة سهلة)
افتح المتصفح واذهب إلى:
```
http://localhost/EFDCLY/database/setup_database.php
```

أو استخدم سطر الأوامر:
```bash
cd C:/xampp/htdocs/EFDCLY/database
php setup_database.php
```

#### 3. الوصول للنظام
```
الرابط: http://localhost/EFDCLY
المستخدم: admin
كلمة المرور: admin123
```

---

## 🔧 التثبيت اليدوي

### 1. إنشاء قاعدة البيانات
```sql
-- في phpMyAdmin أو MySQL Command Line
CREATE DATABASE efdcly_raqeeb CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE efdcly_raqeeb;
SOURCE C:/xampp/htdocs/EFDCLY/database/schema.sql;
SOURCE C:/xampp/htdocs/EFDCLY/database/seed_data.sql;
```

### 2. إنشاء المستخدم المدير
```bash
cd C:/xampp/htdocs/EFDCLY/database
php create_admin.php
```

### 3. تعديل إعدادات الاتصال (إذا لزم الأمر)
```php
// في ملف config/database.php
define('DB_HOST', 'localhost');
define('DB_NAME', 'efdcly_raqeeb');
define('DB_USER', 'root');
define('DB_PASS', '');
```

---

## ⚠️ استكشاف الأخطاء

### خطأ في الاتصال بقاعدة البيانات
- تأكد من تشغيل MySQL في XAMPP
- تحقق من بيانات الاتصال في `config/database.php`

### صفحة بيضاء أو خطأ 500
- تأكد من تفعيل `display_errors` في PHP
- تحقق من ملف error log في XAMPP

### مشاكل في الترميز العربي
- تأكد من ضبط MySQL على utf8mb4
- تحقق من إعدادات المتصفح للترميز

---

## 📞 الدعم

إذا واجهت أي مشاكل:
1. تحقق من ملف README.md للتفاصيل الكاملة
2. راجع ملفات السجل في XAMPP
3. تأكد من تطابق المتطلبات الأساسية

---

**ملاحظة مهمة**: يرجى تغيير كلمة مرور المدير فور تسجيل الدخول الأول!