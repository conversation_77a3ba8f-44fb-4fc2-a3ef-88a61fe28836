# دليل التثبيت السريع - نظام الرقيب

## 🚀 التثبيت السريع (5 دقائق)

### المتطلبات الأساسية
- XAMPP أو WAMP مع PHP 8.0+
- **قاعدة البيانات**: اختر إحدى الخيارات:
  - MySQL 8.0+ (الخيار الافتراضي)
  - Microsoft SQL Server (الخيار الجديد)
- متصفح حديث يدعم JavaScript

### خطوات التثبيت

#### 1. تحضير البيئة
```bash
# تأكد من تشغيل Apache في XAMPP
# انسخ مجلد المشروع إلى htdocs
cp -r EFDCLY/ C:/xampp/htdocs/
```

#### 2. إعداد قاعدة البيانات

##### الخيار الأول: MySQL (الافتراضي)
افتح المتصفح واذهب إلى:
```
http://localhost/EFDCLY/database/setup_database.php
```

##### الخيار الثاني: SQL Server (جديد)
افتح المتصفح واذهب إلى:
```
http://localhost/EFDCLY/database/setup_sqlserver.php
```

أو استخدم سطر الأوامر:
```bash
cd C:/xampp/htdocs/EFDCLY/database
# للـ MySQL
php setup_database.php
# أو للـ SQL Server
php setup_sqlserver.php
```

#### 3. تبديل نوع قاعدة البيانات (اختياري)
```
http://localhost/EFDCLY/database/switch_database.php
```

#### 4. فحص النظام (للتأكد من سلامة التثبيت)
```
# للـ MySQL
http://localhost/EFDCLY/database/check_system.php
# للـ SQL Server
http://localhost/EFDCLY/database/check_sqlserver.php
```

#### 5. الوصول للنظام
```
الرابط: http://localhost/EFDCLY
المستخدم: admin
كلمة المرور: admin123
```

---

## 🔧 التثبيت اليدوي

### للـ MySQL

#### 1. إنشاء قاعدة البيانات
```sql
-- في phpMyAdmin أو MySQL Command Line
CREATE DATABASE efdcly_raqeeb CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE efdcly_raqeeb;
SOURCE C:/xampp/htdocs/EFDCLY/database/schema.sql;
SOURCE C:/xampp/htdocs/EFDCLY/database/seed_data.sql;
```

#### 2. تعديل إعدادات الاتصال (إذا لزم الأمر)
```php
// في ملف config/database.php
define('DB_HOST', 'localhost');
define('DB_NAME', 'efdcly_raqeeb');
define('DB_USER', 'root');
define('DB_PASS', '');
```

### للـ SQL Server

#### 1. متطلبات إضافية
- تثبيت Microsoft SQL Server
- تثبيت Microsoft Drivers for PHP for SQL Server
- تفعيل إضافة pdo_sqlsrv في PHP

#### 2. إنشاء قاعدة البيانات
```sql
-- في SQL Server Management Studio
CREATE DATABASE efdcly_raqeeb COLLATE Arabic_CI_AS;
USE efdcly_raqeeb;
-- ثم تشغيل محتوى ملف schema_sqlserver.sql
-- ثم تشغيل محتوى ملف seed_data_sqlserver.sql
```

#### 3. تعديل إعدادات الاتصال
```php
// في ملف config/database.php
define('DB_HOST', 'YAKHLEF'); // اسم الخادم
define('DB_NAME', 'efdcly_raqeeb');
define('DB_USER', ''); // فارغ للـ Windows Authentication
define('DB_PASS', ''); // فارغ للـ Windows Authentication
```

### إنشاء المستخدم المدير (لكلا النوعين)
```bash
cd C:/xampp/htdocs/EFDCLY/database
php create_admin.php
```

---

## ⚠️ استكشاف الأخطاء

### مشاكل عامة

#### خطأ في الاتصال بقاعدة البيانات
- **MySQL**: تأكد من تشغيل MySQL في XAMPP
- **SQL Server**: تأكد من تشغيل خدمة SQL Server
- تحقق من بيانات الاتصال في `config/database.php`

#### صفحة بيضاء أو خطأ 500
- تأكد من تفعيل `display_errors` في PHP
- تحقق من ملف error log في XAMPP

#### مشاكل في الترميز العربي
- **MySQL**: تأكد من ضبط MySQL على utf8mb4
- **SQL Server**: تأكد من استخدام NVARCHAR للنصوص العربية
- تحقق من إعدادات المتصفح للترميز

### مشاكل خاصة بـ SQL Server

#### إضافة pdo_sqlsrv غير موجودة
```bash
# تحميل وتثبيت Microsoft Drivers for PHP for SQL Server
# من الرابط: https://docs.microsoft.com/en-us/sql/connect/php/download-drivers-php-sql-server
```

#### خطأ في الاتصال بالخادم YAKHLEF
- تأكد من أن اسم الخادم صحيح
- تأكد من تفعيل TCP/IP في SQL Server Configuration Manager
- تأكد من أن المنفذ 1433 مفتوح

#### مشاكل Windows Authentication
- تأكد من تشغيل Apache تحت حساب له صلاحيات على SQL Server
- أو استخدم SQL Server Authentication بدلاً من ذلك

---

## 🔄 تبديل نوع قاعدة البيانات

### أداة التبديل السهلة
يمكنك التبديل بين MySQL و SQL Server بسهولة:
```
http://localhost/EFDCLY/database/switch_database.php
```

### التبديل عبر سطر الأوامر
```bash
cd C:/xampp/htdocs/EFDCLY/database
# التبديل إلى MySQL
php switch_database.php mysql
# التبديل إلى SQL Server
php switch_database.php sqlserver
```

### ملاحظات مهمة
- بعد التبديل، تحتاج لإعداد قاعدة البيانات الجديدة
- البيانات لن تنتقل تلقائياً بين النوعين
- تأكد من وجود الإضافات المطلوبة لكل نوع

---

## 📞 الدعم

إذا واجهت أي مشاكل:
1. تحقق من ملف README.md للتفاصيل الكاملة
2. راجع ملفات السجل في XAMPP
3. تأكد من تطابق المتطلبات الأساسية

---

**ملاحظة مهمة**: يرجى تغيير كلمة مرور المدير فور تسجيل الدخول الأول!