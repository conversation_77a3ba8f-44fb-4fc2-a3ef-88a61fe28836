<?php
/**
 * نظام الرقيب - إعدادات قاعدة البيانات
 * Database Configuration for Registration & Tracking System
 * Created: 2025-08-12
 */

// نوع قاعدة البيانات - يمكن تغييره إلى 'sqlserver' أو 'mysql'
define('DB_TYPE', 'sqlserver'); // تغيير إلى 'sqlserver' لاستخدام SQL Server

// إعدادات قاعدة البيانات
if (DB_TYPE === 'sqlserver') {
    // إعدادات SQL Server
    define('DB_HOST', 'YAKHLEF'); // اسم الخادم
    define('DB_NAME', 'efdcly_raqeeb');
    define('DB_USER', ''); // اتركه فارغ للـ Windows Authentication
    define('DB_PASS', ''); // اتركه فارغ للـ Windows Authentication
    define('DB_PORT', '1433');
    define('DB_CHARSET', 'UTF-8');
} else {
    // إعدادات MySQL (الافتراضي)
    define('DB_HOST', 'localhost');
    define('DB_NAME', 'efdcly_raqeeb');
    define('DB_USER', 'root');
    define('DB_PASS', '');
    define('DB_PORT', '3306');
    define('DB_CHARSET', 'utf8mb4');
}

/**
 * فئة الاتصال بقاعدة البيانات
 * Database Connection Class - Supports both MySQL and SQL Server
 */
class Database {
    private static $instance = null;
    private $connection;

    private function __construct() {
        try {
            if (DB_TYPE === 'sqlserver') {
                $this->connectSqlServer();
            } else {
                $this->connectMySQL();
            }
        } catch (PDOException $e) {
            die("خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage());
        }
    }

    /**
     * الاتصال بـ SQL Server
     */
    private function connectSqlServer() {
        // التحقق من وجود إضافة SQL Server
        if (!extension_loaded('pdo_sqlsrv')) {
            throw new PDOException("إضافة pdo_sqlsrv غير مثبتة. يرجى تثبيت Microsoft Drivers for PHP for SQL Server");
        }

        // إعدادات PDO لـ SQL Server
        $options = [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false
        ];

        // إضافة إعدادات الترميز إذا كانت متوفرة
        if (defined('PDO::SQLSRV_ATTR_ENCODING') && defined('PDO::SQLSRV_ENCODING_UTF8')) {
            $options[PDO::SQLSRV_ATTR_ENCODING] = PDO::SQLSRV_ENCODING_UTF8;
        }

        // إنشاء DSN لـ SQL Server
        if (empty(DB_USER) && empty(DB_PASS)) {
            // استخدام Windows Authentication
            $dsn = "sqlsrv:Server=" . DB_HOST . "," . DB_PORT . ";Database=" . DB_NAME . ";TrustServerCertificate=true";
            $this->connection = new PDO($dsn, null, null, $options);
        } else {
            // استخدام SQL Server Authentication
            $dsn = "sqlsrv:Server=" . DB_HOST . "," . DB_PORT . ";Database=" . DB_NAME . ";TrustServerCertificate=true";
            $this->connection = new PDO($dsn, DB_USER, DB_PASS, $options);
        }
    }

    /**
     * الاتصال بـ MySQL
     */
    private function connectMySQL() {
        // إعدادات PDO لـ MySQL
        $options = [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false,
            PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES " . DB_CHARSET . " COLLATE " . DB_CHARSET . "_unicode_ci"
        ];

        // إنشاء DSN لـ MySQL
        $dsn = "mysql:host=" . DB_HOST . ";port=" . DB_PORT . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET;
        $this->connection = new PDO($dsn, DB_USER, DB_PASS, $options);
    }

    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    public function getConnection() {
        return $this->connection;
    }

    // منع النسخ
    private function __clone() {}

    // منع إلغاء التسلسل
    public function __wakeup() {
        throw new Exception("Cannot unserialize singleton");
    }
}

/**
 * فئة قاعدة البيانات الأساسية
 * Base Database Class
 */
class BaseModel {
    protected $db;
    protected $table;

    public function __construct() {
        $this->db = Database::getInstance()->getConnection();
    }

    /**
     * البحث عن سجل بواسطة المعرف
     * Find record by ID
     */
    public function find($id) {
        $stmt = $this->db->prepare("SELECT * FROM {$this->table} WHERE id = ?");
        $stmt->execute([$id]);
        return $stmt->fetch();
    }

    /**
     * البحث عن جميع السجلات
     * Find all records
     */
    public function findAll($conditions = [], $orderBy = 'id DESC', $limit = null) {
        $sql = "SELECT * FROM {$this->table}";
        $params = [];

        if (!empty($conditions)) {
            $whereClause = [];
            foreach ($conditions as $field => $value) {
                $whereClause[] = "$field = ?";
                $params[] = $value;
            }
            $sql .= " WHERE " . implode(' AND ', $whereClause);
        }

        if ($orderBy) {
            $sql .= " ORDER BY $orderBy";
        }

        if ($limit) {
            $sql .= " LIMIT $limit";
        }

        $stmt = $this->db->prepare($sql);
        $stmt->execute($params);
        return $stmt->fetchAll();
    }

    /**
     * إدراج سجل جديد
     * Insert new record
     */
    public function create($data) {
        $fields = array_keys($data);
        $placeholders = array_fill(0, count($fields), '?');

        $sql = "INSERT INTO {$this->table} (" . implode(', ', $fields) . ") VALUES (" . implode(', ', $placeholders) . ")";

        $stmt = $this->db->prepare($sql);
        $result = $stmt->execute(array_values($data));

        if ($result) {
            return $this->db->lastInsertId();
        }
        return false;
    }

    /**
     * تحديث سجل
     * Update record
     */
    public function update($id, $data) {
        $fields = [];
        foreach (array_keys($data) as $field) {
            $fields[] = "$field = ?";
        }

        $sql = "UPDATE {$this->table} SET " . implode(', ', $fields) . " WHERE id = ?";

        $params = array_values($data);
        $params[] = $id;

        $stmt = $this->db->prepare($sql);
        return $stmt->execute($params);
    }

    /**
     * حذف سجل
     * Delete record
     */
    public function delete($id) {
        $stmt = $this->db->prepare("DELETE FROM {$this->table} WHERE id = ?");
        return $stmt->execute([$id]);
    }

    /**
     * عد السجلات
     * Count records
     */
    public function count($conditions = []) {
        $sql = "SELECT COUNT(*) FROM {$this->table}";
        $params = [];

        if (!empty($conditions)) {
            $whereClause = [];
            foreach ($conditions as $field => $value) {
                $whereClause[] = "$field = ?";
                $params[] = $value;
            }
            $sql .= " WHERE " . implode(' AND ', $whereClause);
        }

        $stmt = $this->db->prepare($sql);
        $stmt->execute($params);
        return $stmt->fetchColumn();
    }
}
?>