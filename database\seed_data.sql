-- نظام الرقيب - البيانات الأولية
-- Initial Data for Registration & Tracking System
-- Created: 2025-08-12

SET NAMES utf8mb4;

-- ===================================
-- البيانات الأساسية للنظام
-- ===================================

-- إدراج الصلاحيات الأساسية
INSERT INTO permissions (name, name_ar, module, description) VALUES
('view_dashboard', 'عرض لوحة التحكم', 'dashboard', 'عرض لوحة التحكم الرئيسية'),
('manage_users', 'إدارة المستخدمين', 'users', 'إضافة وتعديل وحذف المستخدمين'),
('manage_roles', 'إدارة الأدوار', 'roles', 'إدارة الأدوار والصلاحيات'),
('view_facilities', 'عرض المنشآت', 'facilities', 'عرض قائمة المنشآت'),
('manage_facilities', 'إدارة المنشآت', 'facilities', 'إضافة وتعديل المنشآت'),
('approve_registrations', 'اعتماد التسجيلات', 'facilities', 'اعتماد طلبات التسجيل والترخيص'),
('view_shipments', 'عرض الشحنات', 'shipments', 'عرض قائمة الشحنات'),
('manage_shipments', 'إدارة الشحنات', 'shipments', 'إدارة الشحنات والتفتيش'),
('conduct_inspections', 'إجراء التفتيش', 'inspections', 'إجراء عمليات التفتيش الميداني'),
('manage_samples', 'إدارة العينات', 'laboratory', 'إدارة العينات والتحاليل'),
('view_lab_results', 'عرض نتائج المختبر', 'laboratory', 'عرض نتائج التحاليل'),
('approve_lab_results', 'اعتماد نتائج المختبر', 'laboratory', 'اعتماد ومراجعة نتائج التحاليل'),
('manage_complaints', 'إدارة الشكاوى', 'complaints', 'إدارة البلاغات والشكاوى'),
('view_reports', 'عرض التقارير', 'reports', 'عرض التقارير والإحصائيات'),
('generate_reports', 'إنشاء التقارير', 'reports', 'إنشاء التقارير المخصصة'),
('system_admin', 'إدارة النظام', 'system', 'إدارة إعدادات النظام العامة');

-- إدراج الأدوار الأساسية
INSERT INTO roles (name, name_ar, description, is_active) VALUES
('super_admin', 'مدير النظام العام', 'مدير النظام مع صلاحيات كاملة', TRUE),
('admin', 'مدير', 'مدير مع صلاحيات إدارية', TRUE),
('registration_officer', 'موظف التسجيل', 'موظف مختص بتسجيل المنشآت', TRUE),
('inspector', 'مفتش', 'مفتش ميداني', TRUE),
('lab_technician', 'فني مختبر', 'فني تحاليل مختبرية', TRUE),
('lab_supervisor', 'مشرف مختبر', 'مشرف المختبر ومراجع النتائج', TRUE),
('port_officer', 'موظف منفذ', 'موظف في المنافذ', TRUE),
('facility_user', 'مستخدم منشأة', 'مستخدم من المنشآت المسجلة', TRUE);

-- ربط الأدوار بالصلاحيات
-- مدير النظام العام - جميع الصلاحيات
INSERT INTO role_permissions (role_id, permission_id)
SELECT 1, id FROM permissions;

-- مدير - معظم الصلاحيات عدا إدارة النظام
INSERT INTO role_permissions (role_id, permission_id)
SELECT 2, id FROM permissions WHERE name != 'system_admin';

-- موظف التسجيل
INSERT INTO role_permissions (role_id, permission_id)
SELECT 3, id FROM permissions WHERE name IN ('view_dashboard', 'view_facilities', 'manage_facilities', 'approve_registrations', 'view_reports');

-- مفتش
INSERT INTO role_permissions (role_id, permission_id)
SELECT 4, id FROM permissions WHERE name IN ('view_dashboard', 'view_facilities', 'view_shipments', 'manage_shipments', 'conduct_inspections', 'manage_samples', 'view_reports');

-- فني مختبر
INSERT INTO role_permissions (role_id, permission_id)
SELECT 5, id FROM permissions WHERE name IN ('view_dashboard', 'manage_samples', 'view_lab_results');

-- مشرف مختبر
INSERT INTO role_permissions (role_id, permission_id)
SELECT 6, id FROM permissions WHERE name IN ('view_dashboard', 'manage_samples', 'view_lab_results', 'approve_lab_results', 'view_reports');

-- موظف منفذ
INSERT INTO role_permissions (role_id, permission_id)
SELECT 7, id FROM permissions WHERE name IN ('view_dashboard', 'view_facilities', 'view_shipments', 'manage_shipments', 'conduct_inspections', 'view_reports');

-- مستخدم منشأة
INSERT INTO role_permissions (role_id, permission_id)
SELECT 8, id FROM permissions WHERE name IN ('view_dashboard', 'view_facilities', 'view_shipments');

-- ===================================
-- البيانات الأساسية للمنشآت
-- ===================================

-- أنواع المنشآت
INSERT INTO facility_types (name, name_ar, description, is_active) VALUES
('manufacturer', 'مصنع', 'مصنع للأغذية أو الأدوية', TRUE),
('importer', 'مستورد', 'شركة استيراد', TRUE),
('exporter', 'مصدر', 'شركة تصدير', TRUE),
('distributor', 'موزع', 'شركة توزيع', TRUE),
('retailer', 'تاجر تجزئة', 'متجر تجزئة', TRUE),
('warehouse', 'مستودع', 'مستودع تخزين', TRUE);

-- ===================================
-- البيانات الأساسية للمنافذ
-- ===================================

-- المنافذ الليبية الرئيسية
INSERT INTO ports (name, name_ar, type, code, city, is_active) VALUES
('Tripoli Port', 'ميناء طرابلس', 'sea', 'TRP', 'طرابلس', TRUE),
('Benghazi Port', 'ميناء بنغازي', 'sea', 'BNG', 'بنغازي', TRUE),
('Misrata Port', 'ميناء مصراتة', 'sea', 'MSR', 'مصراتة', TRUE),
('Tripoli International Airport', 'مطار طرابلس الدولي', 'air', 'TIA', 'طرابلس', TRUE),
('Benghazi Airport', 'مطار بنغازي', 'air', 'BGA', 'بنغازي', TRUE),
('Ras Ajdir Border', 'منفذ رأس أجدير', 'land', 'RAJ', 'زوارة', TRUE),
('Imssaad Border', 'منفذ امساعد', 'land', 'IMS', 'غدامس', TRUE);

-- ===================================
-- أنواع التحاليل المختبرية
-- ===================================

INSERT INTO test_types (name, name_ar, category, method, unit, normal_range, is_active) VALUES
('Total Plate Count', 'العد الكلي للبكتيريا', 'microbiological', 'ISO 4833', 'CFU/g', '< 10^5', TRUE),
('E.coli', 'الإشريكية القولونية', 'microbiological', 'ISO 16649', 'CFU/g', 'Absent', TRUE),
('Salmonella', 'السالمونيلا', 'microbiological', 'ISO 6579', 'CFU/25g', 'Absent', TRUE),
('Heavy Metals - Lead', 'المعادن الثقيلة - الرصاص', 'chemical', 'ICP-MS', 'mg/kg', '< 0.1', TRUE),
('Heavy Metals - Mercury', 'المعادن الثقيلة - الزئبق', 'chemical', 'ICP-MS', 'mg/kg', '< 0.05', TRUE),
('Pesticide Residues', 'متبقيات المبيدات', 'chemical', 'GC-MS', 'mg/kg', 'MRL Limits', TRUE),
('Moisture Content', 'محتوى الرطوبة', 'physical', 'Gravimetric', '%', 'Product Specific', TRUE),
('pH Value', 'الرقم الهيدروجيني', 'physical', 'Potentiometric', 'pH units', 'Product Specific', TRUE),
('Protein Content', 'محتوى البروتين', 'nutritional', 'Kjeldahl', 'g/100g', 'Label Claim ±20%', TRUE),
('Fat Content', 'محتوى الدهون', 'nutritional', 'Soxhlet', 'g/100g', 'Label Claim ±20%', TRUE);

-- ===================================
-- المخالفات الأساسية
-- ===================================

INSERT INTO violations (violation_code, name, name_ar, category, severity, penalty_amount, description, is_active) VALUES
('V001', 'Expired License', 'ترخيص منتهي الصلاحية', 'Documentation', 'major', 5000.00, 'Operating with expired license', TRUE),
('V002', 'Missing Documentation', 'نقص في الوثائق', 'Documentation', 'minor', 1000.00, 'Required documents not provided', TRUE),
('V003', 'Poor Hygiene Conditions', 'ظروف صحية سيئة', 'Hygiene', 'major', 3000.00, 'Inadequate hygiene and sanitation', TRUE),
('V004', 'Temperature Control Failure', 'فشل في التحكم بدرجة الحرارة', 'Storage', 'critical', 10000.00, 'Cold chain temperature violations', TRUE),
('V005', 'Contaminated Product', 'منتج ملوث', 'Product Quality', 'critical', 15000.00, 'Product contamination detected', TRUE),
('V006', 'Labeling Violations', 'مخالفات في وضع العلامات', 'Labeling', 'minor', 500.00, 'Incorrect or missing product labels', TRUE),
('V007', 'Pest Control Issues', 'مشاكل في مكافحة الآفات', 'Hygiene', 'major', 2000.00, 'Inadequate pest control measures', TRUE),
('V008', 'Staff Training Deficiency', 'نقص في تدريب الموظفين', 'Training', 'minor', 1500.00, 'Staff lacks proper training', TRUE);

-- ===================================
-- الإعدادات العامة للنظام
-- ===================================

INSERT INTO system_settings (setting_key, setting_value, setting_type, description, is_public) VALUES
('system_name', 'نظام الرقيب', 'string', 'اسم النظام', TRUE),
('system_version', '1.0.0', 'string', 'إصدار النظام', TRUE),
('default_language', 'ar', 'string', 'اللغة الافتراضية', TRUE),
('session_timeout', '3600', 'number', 'مهلة انتهاء الجلسة بالثواني', FALSE),
('max_file_upload_size', '10485760', 'number', 'الحد الأقصى لحجم الملف المرفوع بالبايت (10MB)', FALSE),
('email_notifications_enabled', 'true', 'boolean', 'تفعيل الإشعارات عبر البريد الإلكتروني', FALSE),
('sms_notifications_enabled', 'false', 'boolean', 'تفعيل الإشعارات عبر الرسائل القصيرة', FALSE),
('backup_retention_days', '30', 'number', 'عدد أيام الاحتفاظ بالنسخ الاحتياطية', FALSE),
('audit_log_retention_days', '365', 'number', 'عدد أيام الاحتفاظ بسجل العمليات', FALSE),
('license_expiry_warning_days', '30', 'number', 'عدد الأيام للتحذير قبل انتهاء الترخيص', FALSE);