<?php
/**
 * نظام الرقيب - لوحة التحكم الرئيسية
 * Main Dashboard for Registration & Tracking System
 * Created: 2025-08-12
 */

require_once '../../config/config.php';
require_once '../../includes/auth_check.php';

// التحقق من تسجيل الدخول
requireLogin();

// الحصول على بيانات المستخدم الحالي
$currentUser = getCurrentUser();
$userPermissions = AuthCheck::getCurrentUserPermissions();

// إحصائيات سريعة (ستحتاج إلى تطوير هذه الدوال لاحقاً)
$stats = [
    'total_facilities' => 0,
    'active_shipments' => 0,
    'pending_inspections' => 0,
    'lab_samples' => 0
];

$pageTitle = 'لوحة التحكم الرئيسية';
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - <?php echo SYSTEM_NAME; ?></title>

    <!-- Bootstrap 5 RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">

    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <!-- Custom CSS -->
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .navbar-brand {
            font-weight: bold;
            font-size: 1.5rem;
        }

        .sidebar {
            min-height: calc(100vh - 56px);
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .sidebar .nav-link {
            color: rgba(255, 255, 255, 0.8);
            padding: 12px 20px;
            border-radius: 8px;
            margin: 2px 10px;
            transition: all 0.3s ease;
        }

        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            transform: translateX(-5px);
        }

        .stat-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
            transition: transform 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
        }

        .stat-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
        }

        .chart-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
            padding: 20px;
        }

        .welcome-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="fas fa-shield-alt me-2"></i>
                <?php echo SYSTEM_NAME; ?>
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-2"></i>
                            <?php echo $currentUser['full_name']; ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#"><i class="fas fa-user-cog me-2"></i>الملف الشخصي</a></li>
                            <li><a class="dropdown-item" href="#"><i class="fas fa-cog me-2"></i>الإعدادات</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../../modules/auth/logout.php"><i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="position-sticky pt-3">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link active" href="#">
                                <i class="fas fa-tachometer-alt me-2"></i>
                                لوحة التحكم
                            </a>
                        </li>

                        <?php if (hasPermission('view_facilities')): ?>
                        <li class="nav-item">
                            <a class="nav-link" href="../facilities/">
                                <i class="fas fa-building me-2"></i>
                                المنشآت
                            </a>
                        </li>
                        <?php endif; ?>

                        <?php if (hasPermission('view_shipments')): ?>
                        <li class="nav-item">
                            <a class="nav-link" href="../shipments/">
                                <i class="fas fa-ship me-2"></i>
                                الشحنات
                            </a>
                        </li>
                        <?php endif; ?>

                        <?php if (hasPermission('conduct_inspections')): ?>
                        <li class="nav-item">
                            <a class="nav-link" href="../inspection/">
                                <i class="fas fa-search me-2"></i>
                                التفتيش
                            </a>
                        </li>
                        <?php endif; ?>

                        <?php if (hasPermission('manage_samples')): ?>
                        <li class="nav-item">
                            <a class="nav-link" href="../laboratory/">
                                <i class="fas fa-flask me-2"></i>
                                المختبر
                            </a>
                        </li>
                        <?php endif; ?>

                        <?php if (hasPermission('manage_complaints')): ?>
                        <li class="nav-item">
                            <a class="nav-link" href="../complaints/">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                الشكاوى
                            </a>
                        </li>
                        <?php endif; ?>

                        <?php if (hasPermission('view_reports')): ?>
                        <li class="nav-item">
                            <a class="nav-link" href="../reports/">
                                <i class="fas fa-chart-bar me-2"></i>
                                التقارير
                            </a>
                        </li>
                        <?php endif; ?>

                        <?php if (isAdmin()): ?>
                        <li class="nav-item">
                            <a class="nav-link" href="../admin/">
                                <i class="fas fa-cogs me-2"></i>
                                إدارة النظام
                            </a>
                        </li>
                        <?php endif; ?>
                    </ul>
                </div>
            </nav>

            <!-- Main content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="pt-3 pb-2 mb-3">
                    <!-- Welcome Card -->
                    <div class="welcome-card">
                        <h1 class="h2 mb-3">مرحباً، <?php echo $currentUser['full_name']; ?>!</h1>
                        <p class="mb-0">مرحباً بك في نظام الرقيب لتسجيل ومتابعة شركات استيراد وتصدير الأدوية والمواد الغذائية</p>
                        <small class="opacity-75">آخر تسجيل دخول: <?php echo Helper::formatDateTime($currentUser['last_login']); ?></small>
                    </div>

                    <!-- Statistics Cards -->
                    <div class="row mb-4">
                        <div class="col-xl-3 col-md-6 mb-4">
                            <div class="stat-card p-4">
                                <div class="d-flex align-items-center">
                                    <div class="stat-icon bg-primary me-3">
                                        <i class="fas fa-building"></i>
                                    </div>
                                    <div>
                                        <div class="text-muted small">إجمالي المنشآت</div>
                                        <div class="h4 mb-0"><?php echo number_format($stats['total_facilities']); ?></div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-xl-3 col-md-6 mb-4">
                            <div class="stat-card p-4">
                                <div class="d-flex align-items-center">
                                    <div class="stat-icon bg-success me-3">
                                        <i class="fas fa-ship"></i>
                                    </div>
                                    <div>
                                        <div class="text-muted small">الشحنات النشطة</div>
                                        <div class="h4 mb-0"><?php echo number_format($stats['active_shipments']); ?></div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-xl-3 col-md-6 mb-4">
                            <div class="stat-card p-4">
                                <div class="d-flex align-items-center">
                                    <div class="stat-icon bg-warning me-3">
                                        <i class="fas fa-search"></i>
                                    </div>
                                    <div>
                                        <div class="text-muted small">تفتيش معلق</div>
                                        <div class="h4 mb-0"><?php echo number_format($stats['pending_inspections']); ?></div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-xl-3 col-md-6 mb-4">
                            <div class="stat-card p-4">
                                <div class="d-flex align-items-center">
                                    <div class="stat-icon bg-info me-3">
                                        <i class="fas fa-flask"></i>
                                    </div>
                                    <div>
                                        <div class="text-muted small">عينات المختبر</div>
                                        <div class="h4 mb-0"><?php echo number_format($stats['lab_samples']); ?></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Charts Row -->
                    <div class="row mb-4">
                        <div class="col-lg-8 mb-4">
                            <div class="chart-container">
                                <h5 class="mb-3">إحصائيات الشحنات الشهرية</h5>
                                <canvas id="shipmentsChart" height="100"></canvas>
                            </div>
                        </div>

                        <div class="col-lg-4 mb-4">
                            <div class="chart-container">
                                <h5 class="mb-3">توزيع أنواع المنشآت</h5>
                                <canvas id="facilitiesChart" height="200"></canvas>
                            </div>
                        </div>
                    </div>

                    <!-- Recent Activities -->
                    <div class="row">
                        <div class="col-lg-6 mb-4">
                            <div class="chart-container">
                                <h5 class="mb-3">آخر الأنشطة</h5>
                                <div class="list-group list-group-flush">
                                    <div class="list-group-item d-flex align-items-center">
                                        <div class="stat-icon bg-success me-3" style="width: 40px; height: 40px; font-size: 1rem;">
                                            <i class="fas fa-check"></i>
                                        </div>
                                        <div>
                                            <div class="fw-bold">تم اعتماد شحنة جديدة</div>
                                            <small class="text-muted">منذ 5 دقائق</small>
                                        </div>
                                    </div>

                                    <div class="list-group-item d-flex align-items-center">
                                        <div class="stat-icon bg-primary me-3" style="width: 40px; height: 40px; font-size: 1rem;">
                                            <i class="fas fa-building"></i>
                                        </div>
                                        <div>
                                            <div class="fw-bold">تسجيل منشأة جديدة</div>
                                            <small class="text-muted">منذ 15 دقيقة</small>
                                        </div>
                                    </div>

                                    <div class="list-group-item d-flex align-items-center">
                                        <div class="stat-icon bg-warning me-3" style="width: 40px; height: 40px; font-size: 1rem;">
                                            <i class="fas fa-flask"></i>
                                        </div>
                                        <div>
                                            <div class="fw-bold">نتائج تحليل جديدة</div>
                                            <small class="text-muted">منذ 30 دقيقة</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-lg-6 mb-4">
                            <div class="chart-container">
                                <h5 class="mb-3">المهام المعلقة</h5>
                                <div class="list-group list-group-flush">
                                    <div class="list-group-item d-flex justify-content-between align-items-center">
                                        <div>
                                            <div class="fw-bold">مراجعة طلبات التسجيل</div>
                                            <small class="text-muted">5 طلبات في الانتظار</small>
                                        </div>
                                        <span class="badge bg-primary rounded-pill">5</span>
                                    </div>

                                    <div class="list-group-item d-flex justify-content-between align-items-center">
                                        <div>
                                            <div class="fw-bold">تفتيش الشحنات</div>
                                            <small class="text-muted">3 شحنات تحتاج تفتيش</small>
                                        </div>
                                        <span class="badge bg-warning rounded-pill">3</span>
                                    </div>

                                    <div class="list-group-item d-flex justify-content-between align-items-center">
                                        <div>
                                            <div class="fw-bold">اعتماد نتائج المختبر</div>
                                            <small class="text-muted">2 نتيجة تحتاج اعتماد</small>
                                        </div>
                                        <span class="badge bg-info rounded-pill">2</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- Bootstrap 5 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Charts -->
    <script>
        // Shipments Chart
        const shipmentsCtx = document.getElementById('shipmentsChart').getContext('2d');
        new Chart(shipmentsCtx, {
            type: 'line',
            data: {
                labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'],
                datasets: [{
                    label: 'الشحنات الواردة',
                    data: [12, 19, 3, 5, 2, 3],
                    borderColor: '#667eea',
                    backgroundColor: 'rgba(102, 126, 234, 0.1)',
                    tension: 0.4
                }, {
                    label: 'الشحنات الصادرة',
                    data: [2, 3, 20, 5, 1, 4],
                    borderColor: '#764ba2',
                    backgroundColor: 'rgba(118, 75, 162, 0.1)',
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top',
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });

        // Facilities Chart
        const facilitiesCtx = document.getElementById('facilitiesChart').getContext('2d');
        new Chart(facilitiesCtx, {
            type: 'doughnut',
            data: {
                labels: ['مصانع', 'مستوردين', 'مصدرين', 'موزعين'],
                datasets: [{
                    data: [30, 25, 20, 25],
                    backgroundColor: [
                        '#667eea',
                        '#764ba2',
                        '#f093fb',
                        '#f5576c'
                    ]
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                    }
                }
            }
        });
    </script>
</body>
</html>