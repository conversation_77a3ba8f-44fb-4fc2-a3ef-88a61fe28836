-- نظام الرقيب - قاعدة البيانات الشاملة - SQL Server
-- Registration & Tracking System for Food & Drug Import/Export Companies in Libya
-- Created: 2025-08-12
-- Database: Microsoft SQL Server

-- ===================================
-- جداول النظام الأساسية
-- ===================================

-- جدول المستخدمين
CREATE TABLE users (
    id INT IDENTITY(1,1) PRIMARY KEY,
    username NVARCHAR(50) UNIQUE NOT NULL,
    email NVARCHAR(100) UNIQUE NOT NULL,
    password_hash NVARCHAR(255) NOT NULL,
    full_name NVARCHAR(100) NOT NULL,
    phone NVARCHAR(20),
    role_id INT NOT NULL,
    is_active BIT DEFAULT 1,
    last_login DATETIME2 NULL,
    created_at DATETIME2 DEFAULT GETDATE(),
    updated_at DATETIME2 DEFAULT GETDATE(),
    created_by INT
);

-- إنشاء فهارس للمستخدمين
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_role ON users(role_id);

-- جدول الأدوار والصلاحيات
CREATE TABLE roles (
    id INT IDENTITY(1,1) PRIMARY KEY,
    name NVARCHAR(50) NOT NULL,
    name_ar NVARCHAR(100) NOT NULL,
    description NTEXT,
    permissions NTEXT, -- JSON data stored as text
    is_active BIT DEFAULT 1,
    created_at DATETIME2 DEFAULT GETDATE(),
    updated_at DATETIME2 DEFAULT GETDATE()
);

-- جدول الصلاحيات
CREATE TABLE permissions (
    id INT IDENTITY(1,1) PRIMARY KEY,
    name NVARCHAR(50) NOT NULL,
    name_ar NVARCHAR(100) NOT NULL,
    module NVARCHAR(50) NOT NULL,
    description NTEXT,
    created_at DATETIME2 DEFAULT GETDATE()
);

-- جدول ربط الأدوار بالصلاحيات
CREATE TABLE role_permissions (
    role_id INT NOT NULL,
    permission_id INT NOT NULL,
    PRIMARY KEY (role_id, permission_id),
    FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE,
    FOREIGN KEY (permission_id) REFERENCES permissions(id) ON DELETE CASCADE
);

-- ===================================
-- الوحدة الأولى: إدارة المنشآت والتراخيص
-- ===================================

-- جدول أنواع المنشآت
CREATE TABLE facility_types (
    id INT IDENTITY(1,1) PRIMARY KEY,
    name NVARCHAR(100) NOT NULL,
    name_ar NVARCHAR(150) NOT NULL,
    description NTEXT,
    is_active BIT DEFAULT 1,
    created_at DATETIME2 DEFAULT GETDATE()
);

-- جدول المنشآت
CREATE TABLE facilities (
    id INT IDENTITY(1,1) PRIMARY KEY,
    registration_number NVARCHAR(50) UNIQUE NOT NULL,
    commercial_register NVARCHAR(50) NOT NULL,
    name NVARCHAR(200) NOT NULL,
    name_ar NVARCHAR(300) NOT NULL,
    facility_type_id INT NOT NULL,
    license_number NVARCHAR(50),
    license_issue_date DATE,
    license_expiry_date DATE,
    license_status NVARCHAR(20) DEFAULT 'active' CHECK (license_status IN ('active', 'expired', 'suspended', 'cancelled')),
    address NTEXT NOT NULL,
    city NVARCHAR(100) NOT NULL,
    phone NVARCHAR(20),
    email NVARCHAR(100),
    contact_person NVARCHAR(100),
    contact_phone NVARCHAR(20),
    activities NTEXT, -- JSON data stored as text
    products NTEXT, -- JSON data stored as text
    certifications NTEXT, -- JSON data stored as text
    is_active BIT DEFAULT 1,
    created_at DATETIME2 DEFAULT GETDATE(),
    updated_at DATETIME2 DEFAULT GETDATE(),
    created_by INT,
    FOREIGN KEY (facility_type_id) REFERENCES facility_types(id),
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- إنشاء فهارس للمنشآت
CREATE INDEX idx_facilities_registration_number ON facilities(registration_number);
CREATE INDEX idx_facilities_commercial_register ON facilities(commercial_register);
CREATE INDEX idx_facilities_license_number ON facilities(license_number);
CREATE INDEX idx_facilities_license_status ON facilities(license_status);

-- جدول طلبات التسجيل والترخيص
CREATE TABLE registration_requests (
    id INT IDENTITY(1,1) PRIMARY KEY,
    request_number NVARCHAR(50) UNIQUE NOT NULL,
    facility_id INT,
    request_type NVARCHAR(20) NOT NULL CHECK (request_type IN ('new_registration', 'license_renewal', 'data_update')),
    status NVARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'under_review', 'approved', 'rejected', 'requires_documents')),
    submitted_data NTEXT, -- JSON data stored as text
    documents NTEXT, -- JSON data stored as text
    review_notes NTEXT,
    reviewed_by INT,
    reviewed_at DATETIME2 NULL,
    approved_by INT,
    approved_at DATETIME2 NULL,
    created_at DATETIME2 DEFAULT GETDATE(),
    updated_at DATETIME2 DEFAULT GETDATE(),
    FOREIGN KEY (facility_id) REFERENCES facilities(id),
    FOREIGN KEY (reviewed_by) REFERENCES users(id),
    FOREIGN KEY (approved_by) REFERENCES users(id)
);

-- إنشاء فهارس لطلبات التسجيل
CREATE INDEX idx_registration_requests_number ON registration_requests(request_number);
CREATE INDEX idx_registration_requests_status ON registration_requests(status);

-- جدول الشهادات الرقمية
CREATE TABLE digital_certificates (
    id INT IDENTITY(1,1) PRIMARY KEY,
    certificate_number NVARCHAR(50) UNIQUE NOT NULL,
    facility_id INT NOT NULL,
    certificate_type NVARCHAR(20) NOT NULL CHECK (certificate_type IN ('registration', 'license', 'compliance')),
    issue_date DATE NOT NULL,
    expiry_date DATE,
    qr_code NTEXT,
    verification_code NVARCHAR(100) UNIQUE,
    is_valid BIT DEFAULT 1,
    issued_by INT NOT NULL,
    created_at DATETIME2 DEFAULT GETDATE(),
    FOREIGN KEY (facility_id) REFERENCES facilities(id),
    FOREIGN KEY (issued_by) REFERENCES users(id)
);

-- إنشاء فهارس للشهادات الرقمية
CREATE INDEX idx_digital_certificates_number ON digital_certificates(certificate_number);
CREATE INDEX idx_digital_certificates_verification ON digital_certificates(verification_code);

-- ===================================
-- الوحدة الثانية: إدارة المنافذ والشحنات
-- ===================================

-- جدول المنافذ
CREATE TABLE ports (
    id INT IDENTITY(1,1) PRIMARY KEY,
    name NVARCHAR(100) NOT NULL,
    name_ar NVARCHAR(150) NOT NULL,
    type NVARCHAR(10) NOT NULL CHECK (type IN ('sea', 'air', 'land')),
    code NVARCHAR(10) UNIQUE NOT NULL,
    city NVARCHAR(100) NOT NULL,
    is_active BIT DEFAULT 1,
    created_at DATETIME2 DEFAULT GETDATE()
);

-- جدول الشحنات
CREATE TABLE shipments (
    id INT IDENTITY(1,1) PRIMARY KEY,
    shipment_number NVARCHAR(50) UNIQUE NOT NULL,
    facility_id INT NOT NULL,
    port_id INT NOT NULL,
    shipment_type NVARCHAR(10) NOT NULL CHECK (shipment_type IN ('import', 'export')),
    arrival_date DATE,
    notification_date DATETIME2,
    bill_of_lading NVARCHAR(100),
    invoice_number NVARCHAR(100),
    origin_country NVARCHAR(100),
    destination_country NVARCHAR(100),
    product_description NTEXT,
    quantity DECIMAL(10,2),
    unit NVARCHAR(20),
    value DECIMAL(12,2),
    currency NVARCHAR(3),
    status NVARCHAR(20) DEFAULT 'notified' CHECK (status IN ('notified', 'under_inspection', 'released', 'rejected', 'held')),
    documents NTEXT, -- JSON data stored as text
    created_at DATETIME2 DEFAULT GETDATE(),
    updated_at DATETIME2 DEFAULT GETDATE(),
    FOREIGN KEY (facility_id) REFERENCES facilities(id),
    FOREIGN KEY (port_id) REFERENCES ports(id)
);

-- إنشاء فهارس للشحنات
CREATE INDEX idx_shipments_number ON shipments(shipment_number);
CREATE INDEX idx_shipments_status ON shipments(status);
CREATE INDEX idx_shipments_arrival_date ON shipments(arrival_date);

-- جدول التفتيش الميداني
CREATE TABLE inspections (
    id INT IDENTITY(1,1) PRIMARY KEY,
    inspection_number NVARCHAR(50) UNIQUE NOT NULL,
    shipment_id INT NOT NULL,
    inspector_id INT NOT NULL,
    inspection_date DATE NOT NULL,
    inspection_type NVARCHAR(20) NOT NULL CHECK (inspection_type IN ('document_review', 'physical_inspection', 'sampling')),
    checklist_data NTEXT, -- JSON data stored as text
    findings NTEXT,
    photos NTEXT, -- JSON data stored as text
    status NVARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'completed', 'requires_follow_up')),
    created_at DATETIME2 DEFAULT GETDATE(),
    updated_at DATETIME2 DEFAULT GETDATE(),
    FOREIGN KEY (shipment_id) REFERENCES shipments(id),
    FOREIGN KEY (inspector_id) REFERENCES users(id)
);

-- إنشاء فهارس للتفتيش
CREATE INDEX idx_inspections_number ON inspections(inspection_number);
CREATE INDEX idx_inspections_date ON inspections(inspection_date);

-- ===================================
-- الوحدة الثالثة: إدارة المختبرات (LIMS)
-- ===================================

-- جدول العينات
CREATE TABLE samples (
    id INT IDENTITY(1,1) PRIMARY KEY,
    sample_number NVARCHAR(50) UNIQUE NOT NULL,
    barcode NVARCHAR(100) UNIQUE,
    shipment_id INT,
    facility_id INT,
    sample_type NVARCHAR(20) NOT NULL CHECK (sample_type IN ('shipment', 'market', 'complaint')),
    product_name NVARCHAR(200) NOT NULL,
    batch_number NVARCHAR(100),
    production_date DATE,
    expiry_date DATE,
    sampling_date DATE NOT NULL,
    sampling_location NVARCHAR(200),
    sampled_by INT NOT NULL,
    sample_condition NTEXT,
    storage_conditions NVARCHAR(100),
    status NVARCHAR(20) DEFAULT 'received' CHECK (status IN ('received', 'under_analysis', 'completed', 'rejected')),
    created_at DATETIME2 DEFAULT GETDATE(),
    updated_at DATETIME2 DEFAULT GETDATE(),
    FOREIGN KEY (shipment_id) REFERENCES shipments(id),
    FOREIGN KEY (facility_id) REFERENCES facilities(id),
    FOREIGN KEY (sampled_by) REFERENCES users(id)
);

-- إنشاء فهارس للعينات
CREATE INDEX idx_samples_number ON samples(sample_number);
CREATE INDEX idx_samples_barcode ON samples(barcode);
CREATE INDEX idx_samples_status ON samples(status);

-- جدول أنواع التحاليل
CREATE TABLE test_types (
    id INT IDENTITY(1,1) PRIMARY KEY,
    name NVARCHAR(100) NOT NULL,
    name_ar NVARCHAR(150) NOT NULL,
    category NVARCHAR(20) NOT NULL CHECK (category IN ('microbiological', 'chemical', 'physical', 'nutritional')),
    method NVARCHAR(200),
    unit NVARCHAR(50),
    normal_range NVARCHAR(100),
    is_active BIT DEFAULT 1,
    created_at DATETIME2 DEFAULT GETDATE()
);

-- جدول نتائج التحاليل
CREATE TABLE test_results (
    id INT IDENTITY(1,1) PRIMARY KEY,
    sample_id INT NOT NULL,
    test_type_id INT NOT NULL,
    result_value NVARCHAR(200),
    result_status NVARCHAR(20) DEFAULT 'pending' CHECK (result_status IN ('pass', 'fail', 'pending', 'inconclusive')),
    test_date DATE NOT NULL,
    tested_by INT NOT NULL,
    reviewed_by INT,
    reviewed_at DATETIME2 NULL,
    notes NTEXT,
    created_at DATETIME2 DEFAULT GETDATE(),
    updated_at DATETIME2 DEFAULT GETDATE(),
    FOREIGN KEY (sample_id) REFERENCES samples(id),
    FOREIGN KEY (test_type_id) REFERENCES test_types(id),
    FOREIGN KEY (tested_by) REFERENCES users(id),
    FOREIGN KEY (reviewed_by) REFERENCES users(id)
);

-- إنشاء فهارس لنتائج التحاليل
CREATE INDEX idx_test_results_date ON test_results(test_date);
CREATE INDEX idx_test_results_status ON test_results(result_status);

-- جدول شهادات التحليل
CREATE TABLE analysis_certificates (
    id INT IDENTITY(1,1) PRIMARY KEY,
    certificate_number NVARCHAR(50) UNIQUE NOT NULL,
    sample_id INT NOT NULL,
    issue_date DATE NOT NULL,
    overall_result NVARCHAR(10) NOT NULL CHECK (overall_result IN ('pass', 'fail')),
    recommendations NTEXT,
    issued_by INT NOT NULL,
    approved_by INT,
    approved_at DATETIME2 NULL,
    created_at DATETIME2 DEFAULT GETDATE(),
    FOREIGN KEY (sample_id) REFERENCES samples(id),
    FOREIGN KEY (issued_by) REFERENCES users(id),
    FOREIGN KEY (approved_by) REFERENCES users(id)
);

-- إنشاء فهارس لشهادات التحليل
CREATE INDEX idx_analysis_certificates_number ON analysis_certificates(certificate_number);
CREATE INDEX idx_analysis_certificates_date ON analysis_certificates(issue_date);

-- ===================================
-- جداول النظام العامة
-- ===================================

-- جدول الإشعارات
CREATE TABLE notifications (
    id INT IDENTITY(1,1) PRIMARY KEY,
    user_id INT NOT NULL,
    title NVARCHAR(200) NOT NULL,
    message NTEXT NOT NULL,
    type NVARCHAR(10) DEFAULT 'info' CHECK (type IN ('info', 'warning', 'success', 'error')),
    is_read BIT DEFAULT 0,
    related_module NVARCHAR(50),
    related_id INT,
    created_at DATETIME2 DEFAULT GETDATE(),
    FOREIGN KEY (user_id) REFERENCES users(id)
);

-- إنشاء فهارس للإشعارات
CREATE INDEX idx_notifications_user_id ON notifications(user_id);
CREATE INDEX idx_notifications_is_read ON notifications(is_read);

-- جدول سجل العمليات (Audit Trail)
CREATE TABLE audit_log (
    id INT IDENTITY(1,1) PRIMARY KEY,
    user_id INT,
    action NVARCHAR(100) NOT NULL,
    table_name NVARCHAR(50) NOT NULL,
    record_id INT,
    old_values NTEXT, -- JSON data stored as text
    new_values NTEXT, -- JSON data stored as text
    ip_address NVARCHAR(45),
    user_agent NTEXT,
    created_at DATETIME2 DEFAULT GETDATE(),
    FOREIGN KEY (user_id) REFERENCES users(id)
);

-- إنشاء فهارس لسجل العمليات
CREATE INDEX idx_audit_log_user_id ON audit_log(user_id);
CREATE INDEX idx_audit_log_table_name ON audit_log(table_name);
CREATE INDEX idx_audit_log_created_at ON audit_log(created_at);

-- جدول الإعدادات العامة
CREATE TABLE system_settings (
    id INT IDENTITY(1,1) PRIMARY KEY,
    setting_key NVARCHAR(100) UNIQUE NOT NULL,
    setting_value NTEXT,
    setting_type NVARCHAR(10) DEFAULT 'string' CHECK (setting_type IN ('string', 'number', 'boolean', 'json')),
    description NTEXT,
    is_public BIT DEFAULT 0,
    updated_by INT,
    updated_at DATETIME2 DEFAULT GETDATE(),
    FOREIGN KEY (updated_by) REFERENCES users(id)
);

-- إضافة المفاتيح الخارجية المتبقية
ALTER TABLE users ADD FOREIGN KEY (role_id) REFERENCES roles(id);
ALTER TABLE users ADD FOREIGN KEY (created_by) REFERENCES users(id);