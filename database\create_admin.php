<?php
/**
 * نظام الرقيب - إنشاء مستخدم مدير افتراضي
 * Create Default Admin User
 * Created: 2025-08-12
 */

require_once '../config/config.php';
require_once '../modules/auth/User.php';

// بيانات المستخدم المدير الافتراضي
$adminData = [
    'username' => 'admin',
    'email' => '<EMAIL>',
    'password' => 'admin123', // سيتم تشفيرها
    'full_name' => 'مدير النظام',
    'phone' => '0912345678',
    'role_id' => 1, // super_admin
    'is_active' => 1,
    'created_by' => null
];

try {
    $userModel = new User();

    // التحقق من وجود المستخدم
    $existingUser = $userModel->findByUsername($adminData['username']);

    if ($existingUser) {
        echo "المستخدم المدير موجود بالفعل!\n";
        echo "اسم المستخدم: " . $adminData['username'] . "\n";
        echo "كلمة المرور: admin123\n";
    } else {
        // إنشاء المستخدم
        $userId = $userModel->createUser($adminData);

        if ($userId) {
            echo "تم إنشاء المستخدم المدير بنجاح!\n";
            echo "معرف المستخدم: " . $userId . "\n";
            echo "اسم المستخدم: " . $adminData['username'] . "\n";
            echo "كلمة المرور: admin123\n";
            echo "البريد الإلكتروني: " . $adminData['email'] . "\n";
            echo "\nيرجى تغيير كلمة المرور بعد تسجيل الدخول الأول.\n";
        } else {
            echo "فشل في إنشاء المستخدم المدير!\n";
        }
    }

} catch (Exception $e) {
    echo "خطأ: " . $e->getMessage() . "\n";
}
?>