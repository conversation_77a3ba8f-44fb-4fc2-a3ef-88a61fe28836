<?php
/**
 * نظام الرقيب - إنشاء مستخدم مدير افتراضي
 * Create Default Admin User Script
 * Created: 2025-08-12
 */

// التحقق من تشغيل السكريبت من سطر الأوامر أو المتصفح
$isCommandLine = php_sapi_name() === 'cli';

if (!$isCommandLine) {
    echo "<!DOCTYPE html><html dir='rtl'><head><meta charset='UTF-8'><title>إنشاء مستخدم مدير</title>";
    echo "<style>body{font-family:Arial;margin:40px;background:#f5f5f5;} .container{background:white;padding:30px;border-radius:10px;box-shadow:0 2px 10px rgba(0,0,0,0.1);} .success{color:#28a745;} .error{color:#dc3545;} .info{color:#17a2b8;} .warning{color:#ffc107;}</style></head><body>";
    echo "<div class='container'><h1>🛡️ نظام الرقيب - إنشاء مستخدم مدير</h1>";
}

require_once '../config/config.php';
require_once '../modules/auth/User.php';

/**
 * طباعة رسالة مع تنسيق مناسب للمتصفح أو سطر الأوامر
 */
function printMessage($message, $type = 'info') {
    global $isCommandLine;

    if ($isCommandLine) {
        echo $message . "\n";
    } else {
        $class = $type;
        echo "<p class='$class'>$message</p>";
    }
}

/**
 * طباعة عنوان فرعي
 */
function printHeader($header) {
    global $isCommandLine;

    if ($isCommandLine) {
        echo "\n" . str_repeat("=", 50) . "\n";
        echo $header . "\n";
        echo str_repeat("=", 50) . "\n";
    } else {
        echo "<h3>$header</h3>";
    }
}

// بيانات المستخدم المدير الافتراضي
$adminData = [
    'username' => 'admin',
    'email' => '<EMAIL>',
    'password' => 'admin123', // سيتم تشفيرها تلقائياً
    'full_name' => 'مدير النظام العام',
    'phone' => '0912345678',
    'role_id' => 1, // super_admin role
    'is_active' => 1,
    'created_by' => null
];

try {
    printHeader("🔍 فحص الاتصال بقاعدة البيانات");

    // التحقق من الاتصال بقاعدة البيانات
    $db = Database::getInstance()->getConnection();
    printMessage("✅ تم الاتصال بقاعدة البيانات بنجاح", 'success');

    // التحقق من وجود الجداول المطلوبة
    $tables = ['users', 'roles', 'permissions'];
    foreach ($tables as $table) {
        $stmt = $db->query("SHOW TABLES LIKE '$table'");
        if ($stmt->rowCount() == 0) {
            throw new Exception("الجدول '$table' غير موجود. يرجى تشغيل ملف schema.sql أولاً.");
        }
    }
    printMessage("✅ جميع الجداول المطلوبة موجودة", 'success');

    printHeader("👤 إنشاء المستخدم المدير");

    $userModel = new User();

    // التحقق من وجود المستخدم
    $existingUser = $userModel->findByUsername($adminData['username']);

    if ($existingUser) {
        printMessage("⚠️ المستخدم المدير موجود بالفعل!", 'warning');
        printMessage("معرف المستخدم: " . $existingUser['id'], 'info');
        printMessage("اسم المستخدم: " . $existingUser['username'], 'info');
        printMessage("الاسم الكامل: " . $existingUser['full_name'], 'info');
        printMessage("البريد الإلكتروني: " . $existingUser['email'], 'info');
        printMessage("حالة النشاط: " . ($existingUser['is_active'] ? 'نشط' : 'غير نشط'), 'info');
        printMessage("تاريخ الإنشاء: " . Helper::formatDateTime($existingUser['created_at']), 'info');

        if ($existingUser['last_login']) {
            printMessage("آخر تسجيل دخول: " . Helper::formatDateTime($existingUser['last_login']), 'info');
        } else {
            printMessage("لم يسجل دخول من قبل", 'info');
        }

    } else {
        // إنشاء المستخدم الجديد
        printMessage("🔄 جاري إنشاء المستخدم المدير...", 'info');

        $userId = $userModel->createUser($adminData);

        if ($userId) {
            printMessage("🎉 تم إنشاء المستخدم المدير بنجاح!", 'success');
            printMessage("معرف المستخدم: " . $userId, 'success');
            printMessage("اسم المستخدم: " . $adminData['username'], 'success');
            printMessage("كلمة المرور: admin123", 'success');
            printMessage("البريد الإلكتروني: " . $adminData['email'], 'success');
            printMessage("الاسم الكامل: " . $adminData['full_name'], 'success');

            printHeader("🔐 معلومات الأمان");
            printMessage("⚠️ تحذير أمني مهم:", 'warning');
            printMessage("• يرجى تغيير كلمة المرور فور تسجيل الدخول الأول", 'warning');
            printMessage("• تأكد من استخدام كلمة مرور قوية تحتوي على أحرف كبيرة وصغيرة وأرقام ورموز", 'warning');
            printMessage("• لا تشارك بيانات الدخول مع أي شخص آخر", 'warning');

        } else {
            printMessage("❌ فشل في إنشاء المستخدم المدير!", 'error');
        }
    }

    printHeader("🌐 معلومات الوصول للنظام");
    printMessage("رابط النظام: " . BASE_URL, 'info');
    printMessage("رابط تسجيل الدخول: " . BASE_URL . "/modules/auth/login.php", 'info');

} catch (Exception $e) {
    printMessage("❌ خطأ: " . $e->getMessage(), 'error');

    if (!$isCommandLine) {
        echo "<h4>خطوات استكشاف الأخطاء:</h4>";
        echo "<ol>";
        echo "<li>تأكد من تشغيل خادم MySQL</li>";
        echo "<li>تأكد من صحة بيانات الاتصال في ملف config/database.php</li>";
        echo "<li>تأكد من إنشاء قاعدة البيانات وتشغيل ملف schema.sql</li>";
        echo "<li>تأكد من تشغيل ملف seed_data.sql لإدراج البيانات الأولية</li>";
        echo "</ol>";
    }
}

if (!$isCommandLine) {
    echo "</div></body></html>";
}
?>