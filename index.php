<?php
/**
 * نظام الرقيب - الصفحة الرئيسية
 * Main Entry Point for Registration & Tracking System
 * Created: 2025-08-12
 */

// تضمين ملف الإعدادات
require_once 'config/config.php';

// التحقق من حالة تسجيل الدخول
function isLoggedIn() {
    return isset($_SESSION['user_id']) && !empty($_SESSION['user_id']);
}

// التحقق من انتهاء صلاحية الجلسة
function checkSessionTimeout() {
    if (isset($_SESSION['last_activity'])) {
        $inactive = time() - $_SESSION['last_activity'];
        if ($inactive >= SESSION_TIMEOUT) {
            session_unset();
            session_destroy();
            return false;
        }
    }
    $_SESSION['last_activity'] = time();
    return true;
}

// إذا كان المستخدم مسجل الدخول، التحقق من انتهاء الجلسة
if (isLoggedIn()) {
    if (!checkSessionTimeout()) {
        Helper::redirect(BASE_URL . '/modules/auth/login.php?timeout=1');
    }
}

// التوجيه حسب حالة المستخدم
if (!isLoggedIn()) {
    // إعادة التوجيه إلى صفحة تسجيل الدخول
    Helper::redirect(BASE_URL . '/modules/auth/login.php');
} else {
    // إعادة التوجيه إلى لوحة التحكم
    Helper::redirect(BASE_URL . '/modules/dashboard/index.php');
}
?>