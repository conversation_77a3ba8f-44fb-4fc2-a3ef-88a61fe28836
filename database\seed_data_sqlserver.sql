-- نظام الرقيب - البيانات الأولية - SQL Server
-- Initial Data for Registration & Tracking System
-- Created: 2025-08-12

-- ===================================
-- البيانات الأساسية للنظام
-- ===================================

-- إدراج الصلاحيات الأساسية
INSERT INTO permissions (name, name_ar, module, description) VALUES
(N'view_dashboard', N'عرض لوحة التحكم', N'dashboard', N'عرض لوحة التحكم الرئيسية'),
(N'manage_users', N'إدارة المستخدمين', N'users', N'إضافة وتعديل وحذف المستخدمين'),
(N'manage_roles', N'إدارة الأدوار', N'roles', N'إدارة الأدوار والصلاحيات'),
(N'view_facilities', N'عرض المنشآت', N'facilities', N'عرض قائمة المنشآت'),
(N'manage_facilities', N'إدارة المنشآت', N'facilities', N'إضافة وتعديل المنشآت'),
(N'approve_registrations', N'اعتماد التسجيلات', N'facilities', N'اعتماد طلبات التسجيل والترخيص'),
(N'view_shipments', N'عرض الشحنات', N'shipments', N'عرض قائمة الشحنات'),
(N'manage_shipments', N'إدارة الشحنات', N'shipments', N'إدارة الشحنات والتفتيش'),
(N'conduct_inspections', N'إجراء التفتيش', N'inspections', N'إجراء عمليات التفتيش الميداني'),
(N'manage_samples', N'إدارة العينات', N'laboratory', N'إدارة العينات والتحاليل'),
(N'view_lab_results', N'عرض نتائج المختبر', N'laboratory', N'عرض نتائج التحاليل'),
(N'approve_lab_results', N'اعتماد نتائج المختبر', N'laboratory', N'اعتماد ومراجعة نتائج التحاليل'),
(N'manage_complaints', N'إدارة الشكاوى', N'complaints', N'إدارة البلاغات والشكاوى'),
(N'view_reports', N'عرض التقارير', N'reports', N'عرض التقارير والإحصائيات'),
(N'generate_reports', N'إنشاء التقارير', N'reports', N'إنشاء التقارير المخصصة'),
(N'system_admin', N'إدارة النظام', N'system', N'إدارة إعدادات النظام العامة');

-- إدراج الأدوار الأساسية
INSERT INTO roles (name, name_ar, description, is_active) VALUES
(N'super_admin', N'مدير النظام العام', N'مدير النظام مع صلاحيات كاملة', 1),
(N'admin', N'مدير', N'مدير مع صلاحيات إدارية', 1),
(N'registration_officer', N'موظف التسجيل', N'موظف مختص بتسجيل المنشآت', 1),
(N'inspector', N'مفتش', N'مفتش ميداني', 1),
(N'lab_technician', N'فني مختبر', N'فني تحاليل مختبرية', 1),
(N'lab_supervisor', N'مشرف مختبر', N'مشرف المختبر ومراجع النتائج', 1),
(N'port_officer', N'موظف منفذ', N'موظف في المنافذ', 1),
(N'facility_user', N'مستخدم منشأة', N'مستخدم من المنشآت المسجلة', 1);

-- ربط الأدوار بالصلاحيات
-- مدير النظام العام - جميع الصلاحيات
INSERT INTO role_permissions (role_id, permission_id)
SELECT 1, id FROM permissions;

-- مدير - معظم الصلاحيات عدا إدارة النظام
INSERT INTO role_permissions (role_id, permission_id)
SELECT 2, id FROM permissions WHERE name != N'system_admin';

-- موظف التسجيل
INSERT INTO role_permissions (role_id, permission_id)
SELECT 3, id FROM permissions WHERE name IN (N'view_dashboard', N'view_facilities', N'manage_facilities', N'approve_registrations', N'view_reports');

-- مفتش
INSERT INTO role_permissions (role_id, permission_id)
SELECT 4, id FROM permissions WHERE name IN (N'view_dashboard', N'view_facilities', N'view_shipments', N'manage_shipments', N'conduct_inspections', N'manage_samples', N'view_reports');

-- فني مختبر
INSERT INTO role_permissions (role_id, permission_id)
SELECT 5, id FROM permissions WHERE name IN (N'view_dashboard', N'manage_samples', N'view_lab_results');

-- مشرف مختبر
INSERT INTO role_permissions (role_id, permission_id)
SELECT 6, id FROM permissions WHERE name IN (N'view_dashboard', N'manage_samples', N'view_lab_results', N'approve_lab_results', N'view_reports');

-- موظف منفذ
INSERT INTO role_permissions (role_id, permission_id)
SELECT 7, id FROM permissions WHERE name IN (N'view_dashboard', N'view_facilities', N'view_shipments', N'manage_shipments', N'conduct_inspections', N'view_reports');

-- مستخدم منشأة
INSERT INTO role_permissions (role_id, permission_id)
SELECT 8, id FROM permissions WHERE name IN (N'view_dashboard', N'view_facilities', N'view_shipments');

-- ===================================
-- البيانات الأساسية للمنشآت
-- ===================================

-- أنواع المنشآت
INSERT INTO facility_types (name, name_ar, description, is_active) VALUES
(N'manufacturer', N'مصنع', N'مصنع للأغذية أو الأدوية', 1),
(N'importer', N'مستورد', N'شركة استيراد', 1),
(N'exporter', N'مصدر', N'شركة تصدير', 1),
(N'distributor', N'موزع', N'شركة توزيع', 1),
(N'retailer', N'تاجر تجزئة', N'متجر تجزئة', 1),
(N'warehouse', N'مستودع', N'مستودع تخزين', 1);

-- ===================================
-- البيانات الأساسية للمنافذ
-- ===================================

-- المنافذ الليبية الرئيسية
INSERT INTO ports (name, name_ar, type, code, city, is_active) VALUES
(N'Tripoli Port', N'ميناء طرابلس', N'sea', N'TRP', N'طرابلس', 1),
(N'Benghazi Port', N'ميناء بنغازي', N'sea', N'BNG', N'بنغازي', 1),
(N'Misrata Port', N'ميناء مصراتة', N'sea', N'MSR', N'مصراتة', 1),
(N'Tripoli International Airport', N'مطار طرابلس الدولي', N'air', N'TIA', N'طرابلس', 1),
(N'Benghazi Airport', N'مطار بنغازي', N'air', N'BGA', N'بنغازي', 1),
(N'Ras Ajdir Border', N'منفذ رأس أجدير', N'land', N'RAJ', N'زوارة', 1),
(N'Imssaad Border', N'منفذ امساعد', N'land', N'IMS', N'غدامس', 1);

-- ===================================
-- أنواع التحاليل المختبرية
-- ===================================

INSERT INTO test_types (name, name_ar, category, method, unit, normal_range, is_active) VALUES
(N'Total Plate Count', N'العد الكلي للبكتيريا', N'microbiological', N'ISO 4833', N'CFU/g', N'< 10^5', 1),
(N'E.coli', N'الإشريكية القولونية', N'microbiological', N'ISO 16649', N'CFU/g', N'Absent', 1),
(N'Salmonella', N'السالمونيلا', N'microbiological', N'ISO 6579', N'CFU/25g', N'Absent', 1),
(N'Heavy Metals - Lead', N'المعادن الثقيلة - الرصاص', N'chemical', N'ICP-MS', N'mg/kg', N'< 0.1', 1),
(N'Heavy Metals - Mercury', N'المعادن الثقيلة - الزئبق', N'chemical', N'ICP-MS', N'mg/kg', N'< 0.05', 1),
(N'Pesticide Residues', N'متبقيات المبيدات', N'chemical', N'GC-MS', N'mg/kg', N'MRL Limits', 1),
(N'Moisture Content', N'محتوى الرطوبة', N'physical', N'Gravimetric', N'%', N'Product Specific', 1),
(N'pH Value', N'الرقم الهيدروجيني', N'physical', N'Potentiometric', N'pH units', N'Product Specific', 1),
(N'Protein Content', N'محتوى البروتين', N'nutritional', N'Kjeldahl', N'g/100g', N'Label Claim ±20%', 1),
(N'Fat Content', N'محتوى الدهون', N'nutritional', N'Soxhlet', N'g/100g', N'Label Claim ±20%', 1);

-- ===================================
-- الإعدادات العامة للنظام
-- ===================================

INSERT INTO system_settings (setting_key, setting_value, setting_type, description, is_public) VALUES
(N'system_name', N'نظام الرقيب', N'string', N'اسم النظام', 1),
(N'system_version', N'1.0.0', N'string', N'إصدار النظام', 1),
(N'default_language', N'ar', N'string', N'اللغة الافتراضية', 1),
(N'session_timeout', N'3600', N'number', N'مهلة انتهاء الجلسة بالثواني', 0),
(N'max_file_upload_size', N'10485760', N'number', N'الحد الأقصى لحجم الملف المرفوع بالبايت (10MB)', 0),
(N'email_notifications_enabled', N'true', N'boolean', N'تفعيل الإشعارات عبر البريد الإلكتروني', 0),
(N'sms_notifications_enabled', N'false', N'boolean', N'تفعيل الإشعارات عبر الرسائل القصيرة', 0),
(N'backup_retention_days', N'30', N'number', N'عدد أيام الاحتفاظ بالنسخ الاحتياطية', 0),
(N'audit_log_retention_days', N'365', N'number', N'عدد أيام الاحتفاظ بسجل العمليات', 0),
(N'license_expiry_warning_days', N'30', N'number', N'عدد الأيام للتحذير قبل انتهاء الترخيص', 0);